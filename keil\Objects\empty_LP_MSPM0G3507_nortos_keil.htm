<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\empty_LP_MSPM0G3507_nortos_keil.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\empty_LP_MSPM0G3507_nortos_keil.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6210000: Last Updated: Tue Jul  8 19:53:18 2025
<BR><P>
<H3>Maximum Stack Usage =        312 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; PID_operation &rArr; get_encoder_count_R &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">SVC_Handler</a><BR>
 <LI><a href="#[4]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">PendSV_Handler</a><BR>
 <LI><a href="#[5]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">SysTick_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1b]">AES_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[c]">CANFD0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[d]">DAC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1d]">DMA_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[19]">I2C0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1a]">I2C1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1c]">RTC_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[e]">SPI0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[f]">SPI1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[15]">TIMA0_IRQHandler</a> from hw_timer.o(.text.TIMA0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[16]">TIMA1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[13]">TIMG0_IRQHandler</a> from empty.o(.text.TIMG0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[18]">TIMG12_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[14]">TIMG6_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[17]">TIMG7_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[12]">UART0_IRQHandler</a> from empty.o(.text.UART0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[10]">UART1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[11]">UART2_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[9]">UART3_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1f]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_mspm0g350x_uvision.o(.text)
 <LI><a href="#[21]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[20]">fputc</a> from usart.o(.text.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[1e]">main</a> from empty.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1f]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(.text)
</UL>
<P><STRONG><a name="[d5]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[22]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[3e]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[d6]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[d7]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[d8]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[d9]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[da]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>AES_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>Default_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>

<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>I2C0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>UART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[db]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[25]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[dd]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[de]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[27]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[28]"></a>__aeabi_fadd</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalculatePositionFromDigital
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calculate
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Update
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
</UL>

<P><STRONG><a name="[2b]"></a>__aeabi_fsub</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calculate
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Update
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[2c]"></a>__aeabi_frsub</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[59]"></a>__aeabi_fmul</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, fmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calculate
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Update
</UL>

<P><STRONG><a name="[2d]"></a>__aeabi_fdiv</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalculatePositionFromDigital
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calculate
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Update
</UL>

<P><STRONG><a name="[2e]"></a>__aeabi_dadd</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[33]"></a>__aeabi_dsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[34]"></a>__aeabi_drsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[35]"></a>__aeabi_dmul</STRONG> (Thumb, 202 bytes, Stack size 72 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_encoder_count_R
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_encoder_count_L
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[36]"></a>__aeabi_ddiv</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[61]"></a>__aeabi_fcmple</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calculate
</UL>

<P><STRONG><a name="[37]"></a>__aeabi_i2f</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_i2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalculatePositionFromDigital
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[38]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_encoder_count_R
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_encoder_count_L
</UL>

<P><STRONG><a name="[66]"></a>__aeabi_f2iz</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, ffixi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[39]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __aeabi_d2iz &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_encoder_count_R
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_encoder_count_L
</UL>

<P><STRONG><a name="[67]"></a>__aeabi_f2d</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[df]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, uidiv_div0.o(.text), UNUSED)

<P><STRONG><a name="[d4]"></a>__aeabi_uidivmod</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, uidiv_div0.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[3b]"></a>__aeabi_uldivmod</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[2f]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[e0]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[3a]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[e1]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[30]"></a>__aeabi_lasr</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[e2]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[e3]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[2a]"></a>_float_round</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[29]"></a>_float_epilogue</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
</UL>

<P><STRONG><a name="[32]"></a>_double_round</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[31]"></a>_double_epilogue</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_clz
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>

<P><STRONG><a name="[3d]"></a>__aeabi_d2ulz</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[d1]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[23]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[e4]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[3f]"></a>AB_Control</STRONG> (Thumb, 168 bytes, Stack size 56 bytes, bsp_tb6612.o(.text.AB_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AB_Control &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[43]"></a>CalculatePositionFromDigital</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, sensor.o(.text.CalculatePositionFromDigital))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = CalculatePositionFromDigital &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdatePosition
</UL>

<P><STRONG><a name="[ae]"></a>DL_Common_delayCycles</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dl_common.o(.text.DL_Common_delayCycles))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[bd]"></a>DL_I2C_fillControllerTXFIFO</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_I2C_fillControllerTXFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_IIC_ReadByte
</UL>

<P><STRONG><a name="[bf]"></a>DL_I2C_flushControllerTXFIFO</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_IIC_ReadByte
</UL>

<P><STRONG><a name="[7b]"></a>DL_I2C_setClockConfig</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, dl_i2c.o(.text.DL_I2C_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_I2C_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[8b]"></a>DL_SPI_init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, dl_spi.o(.text.DL_SPI_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_SPI_init
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_LCD_init
</UL>

<P><STRONG><a name="[8a]"></a>DL_SPI_setClockConfig</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, dl_spi.o(.text.DL_SPI_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_SPI_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_LCD_init
</UL>

<P><STRONG><a name="[92]"></a>DL_SYSCTL_configSYSPLL</STRONG> (Thumb, 192 bytes, Stack size 36 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DL_SYSCTL_configSYSPLL
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[91]"></a>DL_SYSCTL_setHFCLKSourceHFXTParams</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_SYSCTL_setHFCLKSourceHFXTParams
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[94]"></a>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[83]"></a>DL_Timer_initFourCCPWMMode</STRONG> (Thumb, 264 bytes, Stack size 20 bytes, dl_timer.o(.text.DL_Timer_initFourCCPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_Timer_initFourCCPWMMode
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
</UL>

<P><STRONG><a name="[9a]"></a>DL_Timer_initTimerMode</STRONG> (Thumb, 236 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Timer_initTimerMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Timer_initTimerMode
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_TICK_init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
</UL>

<P><STRONG><a name="[85]"></a>DL_Timer_setCaptCompUpdateMethod</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCaptCompUpdateMethod
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
</UL>

<P><STRONG><a name="[84]"></a>DL_Timer_setCaptureCompareOutCtl</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCaptureCompareOutCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
</UL>

<P><STRONG><a name="[42]"></a>DL_Timer_setCaptureCompareValue</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareValue))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AB_Control
</UL>

<P><STRONG><a name="[82]"></a>DL_Timer_setClockConfig</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_TICK_init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
</UL>

<P><STRONG><a name="[9f]"></a>DL_UART_init</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_UART_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[9e]"></a>DL_UART_setClockConfig</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 292 bytes, Stack size 16 bytes, bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GROUP1_IRQHandler &rArr; DL_GPIO_clearInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearInterruptStatus
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_readPins
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_getEnabledInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>IIC_Get_Digtal</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, hardware_iic.o(.text.IIC_Get_Digtal))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = IIC_Get_Digtal &rArr; IIC_ReadByte &rArr; hardware_IIC_ReadByte &rArr; DL_I2C_startControllerTransfer &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_ReadByte
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdatePosition
</UL>

<P><STRONG><a name="[56]"></a>IIC_ReadByte</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, hardware_iic.o(.text.IIC_ReadByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = IIC_ReadByte &rArr; hardware_IIC_ReadByte &rArr; DL_I2C_startControllerTransfer &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_IIC_ReadByte
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Get_Digtal
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ping
</UL>

<P><STRONG><a name="[c3]"></a>Kalman_Init</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, filter.o(.text.Kalman_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Kalman_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[58]"></a>Kalman_Update</STRONG> (Thumb, 108 bytes, Stack size 40 bytes, filter.o(.text.Kalman_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = Kalman_Update &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[5a]"></a>LCD_WR_DATA8</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, hw_lcd.o(.text.LCD_WR_DATA8))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = LCD_WR_DATA8 &rArr; LCD_Writ_Bus &rArr; spi_write_bus &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Writ_Bus
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[5c]"></a>LCD_WR_REG</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, hw_lcd.o(.text.LCD_WR_REG))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = LCD_WR_REG &rArr; LCD_Writ_Bus &rArr; spi_write_bus &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Writ_Bus
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[5b]"></a>LCD_Writ_Bus</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, hw_lcd.o(.text.LCD_Writ_Bus))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LCD_Writ_Bus &rArr; spi_write_bus &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_bus
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_DATA8
</UL>

<P><STRONG><a name="[60]"></a>PID_Calculate</STRONG> (Thumb, 180 bytes, Stack size 72 bytes, pid.o(.text.PID_Calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = PID_Calculate &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[c2]"></a>PID_Init</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, pid.o(.text.PID_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = PID_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[62]"></a>PID_operation</STRONG> (Thumb, 284 bytes, Stack size 104 bytes, empty.o(.text.PID_operation))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = PID_operation &rArr; get_encoder_count_R &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AB_Control
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calculate
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Update
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_encoder_count_R
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_encoder_count_L
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdatePosition
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[69]"></a>Ping</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, hardware_iic.o(.text.Ping))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Ping &rArr; IIC_ReadByte &rArr; hardware_IIC_ReadByte &rArr; DL_I2C_startControllerTransfer &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_ReadByte
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6a]"></a>SYSCFG_DL_GPIO_init</STRONG> (Thumb, 456 bytes, Stack size 88 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = SYSCFG_DL_GPIO_init &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableInterrupt
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearInterruptStatus
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setUpperPinsPolarity
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setLowerPinsPolarity
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInputFeatures
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutputFeatures
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralInputFunction
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableHiZ
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralInputFunctionFeatures
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralOutputFunction
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralAnalogFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[7a]"></a>SYSCFG_DL_I2C_0_init</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_I2C_0_init &rArr; DL_I2C_setControllerRXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setClockConfig
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_enableController
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_enableControllerClockStretching
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setControllerRXFIFOThreshold
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setControllerTXFIFOThreshold
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setTimerPeriod
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_resetControllerTransfer
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_enableAnalogGlitchFilter
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setAnalogGlitchFilterPulseWidth
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[81]"></a>SYSCFG_DL_PWM_0_init</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SYSCFG_DL_PWM_0_init &rArr; DL_Timer_setCounterControl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initFourCCPWMMode
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCCPDirection
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterControl
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[88]"></a>SYSCFG_DL_PWM_LED_init</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SYSCFG_DL_PWM_LED_init &rArr; DL_Timer_setCounterControl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initFourCCPWMMode
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCCPDirection
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterControl
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[89]"></a>SYSCFG_DL_SPI_LCD_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_SPI_LCD_init &rArr; DL_SPI_setFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setClockConfig
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_enable
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setFIFOThreshold
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setBitRateSerialClockDivider
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[8d]"></a>SYSCFG_DL_SYSCTL_init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SYSCFG_DL_SYSCTL_init &rArr; DL_SYSCTL_configSYSPLL
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_enableMFCLK
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setULPCLKDivider
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableSYSPLL
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableHFXT
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setFlashWaitState
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[96]"></a>SYSCFG_DL_SYSTICK_init</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SYSCFG_DL_SYSTICK_init &rArr; DL_SYSTICK_init
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSTICK_enable
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSTICK_init
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[99]"></a>SYSCFG_DL_TIMER_0_init</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SYSCFG_DL_TIMER_0_init &rArr; DL_Timer_initTimerMode
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableInterrupt
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[9c]"></a>SYSCFG_DL_TIMER_TICK_init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SYSCFG_DL_TIMER_TICK_init &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableInterrupt
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[9d]"></a>SYSCFG_DL_UART_0_init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_0_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableInterrupt
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a2]"></a>SYSCFG_DL_init</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SYSCFG_DL_init &rArr; SYSCFG_DL_GPIO_init &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_LCD_init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_TICK_init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a3]"></a>SYSCFG_DL_initPower</STRONG> (Thumb, 140 bytes, Stack size 48 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_initPower))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SYSCFG_DL_initPower &rArr; DL_SPI_enablePower
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_enablePower
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enablePower
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_enablePower
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enablePower
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enablePower
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_reset
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_reset
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_reset
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_reset
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[15]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, hw_timer.o(.text.TIMA0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIMA0_IRQHandler &rArr; DL_Timer_getPendingInterrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_getPendingInterrupt
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_update_R
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_update_L
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, empty.o(.text.TIMG0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIMG0_IRQHandler &rArr; DL_Timer_getPendingInterrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_getPendingInterrupt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>UART0_IRQHandler</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, empty.o(.text.UART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART0_IRQHandler &rArr; uart0_send_char &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_send_char
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveData
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getPendingInterrupt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>UpdatePosition</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, sensor.o(.text.UpdatePosition))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = UpdatePosition &rArr; IIC_Get_Digtal &rArr; IIC_ReadByte &rArr; hardware_IIC_ReadByte &rArr; DL_I2C_startControllerTransfer &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalculatePositionFromDigital
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Get_Digtal
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[b6]"></a>delay_ms</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, time.o(.text.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = delay_ms &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b7]"></a>delay_us</STRONG> (Thumb, 108 bytes, Stack size 20 bytes, time.o(.text.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[b8]"></a>encoder_init</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, bsp_motor_hallencoder.o(.text.encoder_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = encoder_init &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b0]"></a>encoder_update_L</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, bsp_motor_hallencoder.o(.text.encoder_update_L))
<BR><BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMA0_IRQHandler
</UL>

<P><STRONG><a name="[b1]"></a>encoder_update_R</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, bsp_motor_hallencoder.o(.text.encoder_update_R))
<BR><BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMA0_IRQHandler
</UL>

<P><STRONG><a name="[20]"></a>fputc</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, usart.o(.text.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitData
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_isBusy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[64]"></a>get_encoder_count_L</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, bsp_motor_hallencoder.o(.text.get_encoder_count_L))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = get_encoder_count_L &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[65]"></a>get_encoder_count_R</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, bsp_motor_hallencoder.o(.text.get_encoder_count_R))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = get_encoder_count_R &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[57]"></a>hardware_IIC_ReadByte</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, hw_i2c.o(.text.hardware_IIC_ReadByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = hardware_IIC_ReadByte &rArr; DL_I2C_startControllerTransfer &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_flushControllerTXFIFO
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_fillControllerTXFIFO
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_receiveControllerData
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_startControllerTransfer
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_getControllerStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_ReadByte
</UL>

<P><STRONG><a name="[c1]"></a>lcd_init</STRONG> (Thumb, 448 bytes, Stack size 80 bytes, hw_lcd.o(.text.lcd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = lcd_init &rArr; LCD_WR_REG &rArr; LCD_Writ_Bus &rArr; spi_write_bus &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_DATA8
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e]"></a>main</STRONG> (Thumb, 276 bytes, Stack size 72 bytes, empty.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = main &rArr; PID_operation &rArr; get_encoder_count_R &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ping
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_send_string
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_ClearPendingIRQ
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sprintf
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[5f]"></a>spi_write_bus</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, hw_lcd.o(.text.spi_write_bus))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = spi_write_bus &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_isBusy
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_transmitData8
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Writ_Bus
</UL>

<P><STRONG><a name="[c4]"></a>timer_init</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, hw_timer.o(.text.timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = timer_init &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b5]"></a>uart0_send_char</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, usart.o(.text.uart0_send_char))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart0_send_char &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_transmitData
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_isBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_send_string
</UL>

<P><STRONG><a name="[c8]"></a>uart0_send_string</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, usart.o(.text.uart0_send_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = uart0_send_string &rArr; uart0_send_char &rArr; DL_UART_transmitData
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_send_char
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cd]"></a>__0printf</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e5]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[e6]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[e7]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[68]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = printf
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_operation
</UL>

<P><STRONG><a name="[cf]"></a>__0sprintf</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e8]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[e9]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[ea]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[c7]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3c]"></a>__ARM_clz</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, depilogue.o(i.__ARM_clz))
<BR><BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[eb]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[ec]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[ed]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[c5]"></a>__NVIC_ClearPendingIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, empty.o(.text.__NVIC_ClearPendingIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c6]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, empty.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b3]"></a>DL_UART_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, empty.o(.text.DL_UART_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[b4]"></a>DL_UART_receiveData</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, empty.o(.text.DL_UART_receiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_receiveData
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[b2]"></a>DL_Timer_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, empty.o(.text.DL_Timer_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[a4]"></a>DL_GPIO_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[a5]"></a>DL_Timer_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[a6]"></a>DL_I2C_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[a7]"></a>DL_UART_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[a8]"></a>DL_SPI_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SPI_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[a9]"></a>DL_GPIO_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[aa]"></a>DL_Timer_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[ab]"></a>DL_I2C_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[ac]"></a>DL_UART_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[ad]"></a>DL_SPI_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SPI_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[6b]"></a>DL_GPIO_initPeripheralAnalogFunction</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initPeripheralAnalogFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[6c]"></a>DL_GPIO_initPeripheralOutputFunction</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_initPeripheralOutputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[6d]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[6e]"></a>DL_GPIO_initPeripheralInputFunctionFeatures</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initPeripheralInputFunctionFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[6f]"></a>DL_GPIO_enableHiZ</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_enableHiZ
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[70]"></a>DL_GPIO_initPeripheralInputFunction</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_initPeripheralInputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[71]"></a>DL_GPIO_initDigitalOutputFeatures</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initDigitalOutputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[72]"></a>DL_GPIO_initDigitalInputFeatures</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[73]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[74]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[75]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[76]"></a>DL_GPIO_setLowerPinsPolarity</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setLowerPinsPolarity
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[77]"></a>DL_GPIO_setUpperPinsPolarity</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setUpperPinsPolarity
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[78]"></a>DL_GPIO_clearInterruptStatus</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[79]"></a>DL_GPIO_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[8e]"></a>DL_SYSCTL_setBORThreshold</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[4c]"></a>DL_SYSCTL_setFlashWaitState</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setFlashWaitState &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[4d]"></a>DL_SYSCTL_setSYSOSCFreq</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setSYSOSCFreq &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[8f]"></a>DL_SYSCTL_disableHFXT</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[90]"></a>DL_SYSCTL_disableSYSPLL</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[4e]"></a>DL_SYSCTL_setULPCLKDivider</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setULPCLKDivider &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[93]"></a>DL_SYSCTL_enableMFCLK</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[95]"></a>__NVIC_SetPriority</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_TICK_init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[4f]"></a>DL_Timer_setCounterControl</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, ti_msp_dl_config.o(.text.DL_Timer_setCounterControl))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_Timer_setCounterControl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
</UL>

<P><STRONG><a name="[86]"></a>DL_Timer_enableClock</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_enableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_TICK_init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
</UL>

<P><STRONG><a name="[87]"></a>DL_Timer_setCCPDirection</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCCPDirection
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_LED_init
</UL>

<P><STRONG><a name="[9b]"></a>DL_Timer_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_TICK_init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
</UL>

<P><STRONG><a name="[44]"></a>DL_I2C_setAnalogGlitchFilterPulseWidth</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_I2C_setAnalogGlitchFilterPulseWidth &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[7c]"></a>DL_I2C_enableAnalogGlitchFilter</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_enableAnalogGlitchFilter
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[7d]"></a>DL_I2C_resetControllerTransfer</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_resetControllerTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[7e]"></a>DL_I2C_setTimerPeriod</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_I2C_setTimerPeriod
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[47]"></a>DL_I2C_setControllerTXFIFOThreshold</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_I2C_setControllerTXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[46]"></a>DL_I2C_setControllerRXFIFOThreshold</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_I2C_setControllerRXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[7f]"></a>DL_I2C_enableControllerClockStretching</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_enableControllerClockStretching
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[80]"></a>DL_I2C_enableController</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_enableController))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_enableController
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[51]"></a>DL_UART_setOversampling</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_UART_setOversampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_UART_setOversampling &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[50]"></a>DL_UART_setBaudRateDivisor</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[a0]"></a>DL_UART_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_UART_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[a1]"></a>DL_UART_enable</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[4a]"></a>DL_SPI_setBitRateSerialClockDivider</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SPI_setBitRateSerialClockDivider &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_LCD_init
</UL>

<P><STRONG><a name="[4b]"></a>DL_SPI_setFIFOThreshold</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_SPI_setFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_LCD_init
</UL>

<P><STRONG><a name="[8c]"></a>DL_SPI_enable</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SPI_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_LCD_init
</UL>

<P><STRONG><a name="[97]"></a>DL_SYSTICK_init</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SYSTICK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSTICK_init
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
</UL>

<P><STRONG><a name="[98]"></a>DL_SYSTICK_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSTICK_enable))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
</UL>

<P><STRONG><a name="[45]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setFIFOThreshold
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setBitRateSerialClockDivider
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setControllerRXFIFOThreshold
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setControllerTXFIFOThreshold
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setAnalogGlitchFilterPulseWidth
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterControl
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setULPCLKDivider
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setFlashWaitState
</UL>

<P><STRONG><a name="[bb]"></a>DL_UART_isBusy</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, usart.o(.text.DL_UART_isBusy))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_isBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_send_char
</UL>

<P><STRONG><a name="[bc]"></a>DL_UART_transmitData</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usart.o(.text.DL_UART_transmitData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_transmitData
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_send_char
</UL>

<P><STRONG><a name="[c9]"></a>DL_SPI_transmitData8</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, hw_lcd.o(.text.DL_SPI_transmitData8))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_SPI_transmitData8
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_bus
</UL>

<P><STRONG><a name="[ca]"></a>DL_SPI_isBusy</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, hw_lcd.o(.text.DL_SPI_isBusy))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_isBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_write_bus
</UL>

<P><STRONG><a name="[5d]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, hw_lcd.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Writ_Bus
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[5e]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, hw_lcd.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WR_REG
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Writ_Bus
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[b9]"></a>__NVIC_ClearPendingIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_init
</UL>

<P><STRONG><a name="[ba]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_init
</UL>

<P><STRONG><a name="[52]"></a>DL_GPIO_getEnabledInterruptStatus</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_getEnabledInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[53]"></a>DL_GPIO_readPins</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bsp_motor_hallencoder.o(.text.DL_GPIO_readPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_readPins
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[54]"></a>DL_GPIO_clearInterruptStatus</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[40]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bsp_tb6612.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AB_Control
</UL>

<P><STRONG><a name="[41]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bsp_tb6612.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AB_Control
</UL>

<P><STRONG><a name="[cb]"></a>__NVIC_ClearPendingIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, hw_timer.o(.text.__NVIC_ClearPendingIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
</UL>

<P><STRONG><a name="[cc]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, hw_timer.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
</UL>

<P><STRONG><a name="[af]"></a>DL_Timer_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, hw_timer.o(.text.DL_Timer_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMA0_IRQHandler
</UL>

<P><STRONG><a name="[be]"></a>DL_I2C_getControllerStatus</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, hw_i2c.o(.text.DL_I2C_getControllerStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_getControllerStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_IIC_ReadByte
</UL>

<P><STRONG><a name="[48]"></a>DL_I2C_startControllerTransfer</STRONG> (Thumb, 64 bytes, Stack size 32 bytes, hw_i2c.o(.text.DL_I2C_startControllerTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_I2C_startControllerTransfer &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_IIC_ReadByte
</UL>

<P><STRONG><a name="[49]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, hw_i2c.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_startControllerTransfer
</UL>

<P><STRONG><a name="[c0]"></a>DL_I2C_receiveControllerData</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, hw_i2c.o(.text.DL_I2C_receiveControllerData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_receiveControllerData
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_IIC_ReadByte
</UL>

<P><STRONG><a name="[d0]"></a>_fp_digits</STRONG> (Thumb, 344 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ce]"></a>_printf_core</STRONG> (Thumb, 1754 bytes, Stack size 128 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[d3]"></a>_printf_post_padding</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[d2]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[21]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>


#include "hardware_iic.h"
#include "hw_timer.h"
unsigned char IIC_ReadByte(unsigned char Salve_Adress,unsigned char Reg_Address)
{
	return hardware_IIC_ReadByte(Salve_Adress,Reg_Address);
}
unsigned char IIC_ReadBytes(unsigned char Salve_Adress,unsigned char Reg_Address,unsigned char *Result,unsigned char len)
{
	return hardware_IIC_ReadBytes(Salve_Adress,Reg_Address,Result,len);
}
unsigned char IIC_WriteByte(unsigned char Salve_Adress,unsigned char Reg_Address,unsigned char data)
{
	return hardware_IIC_WirteByte(Salve_Adress,Reg_Address,data);
}
unsigned char IIC_WriteBytes(unsigned char Salve_Adress,unsigned char Reg_Address,unsigned char *data,unsigned char len)
{
	return hardware_IIC_WirteBytes(Salve_Adress,Reg_Address,data,len);
}
unsigned char Ping(void)
{
	unsigned char dat;
	dat=IIC_ReadByte(GW_GRAY_ADDR_DEF,GW_GRAY_PING);
	if(dat==GW_GRAY_PING_OK)
	{
			return 0;
	}	
	else return 1;
}
unsigned char IIC_Get_Digtal(void)
{
	unsigned char dat;
	dat=IIC_ReadByte(GW_GRAY_ADDR_DEF,GW_GRAY_DIGITAL_MODE);
	return dat;
}
unsigned char IIC_Get_Anolog(unsigned char * Result,unsigned char len)
{
	if(IIC_ReadBytes(GW_GRAY_ADDR_DEF,GW_GRAY_ANALOG_BASE_,Result,len))return 1;
	else return 0;
}
unsigned char IIC_Get_Single_Anolog(unsigned char Channel)
{
	unsigned char dat;
	IIC_ReadBytes(GW_GRAY_ADDR_DEF,GW_GRAY_ANALOG(Channel),&dat,1);
	return dat;
}
uint8_t IIC_write_buff[10]={0};//IICд�����õ�buff
unsigned char IIC_Get_Normalize(unsigned char * Result,unsigned char len)
{
	    /*��һ��*/
			/*��һ������˼�ǣ�ʹ����̽ͷ����ͬһ����ɫ���ߺ�ɫ�£�������һ�µ�*/
			/*�����У׼���йصģ�����ͨ��У׼���ݸ�ģ������������������һ����*/
	IIC_write_buff[0]=GW_GRAY_ANALOG_NORMALIZE;//��һ��ʹ�ܼĴ��������粻�洢
	IIC_write_buff[1]=0xff;//ȫͨ������
	IIC_WriteBytes(GW_GRAY_ADDR_DEF,GW_GRAY_ANALOG_NORMALIZE ,&IIC_write_buff[1]/*����+����*/, 2 /*д����������*/);
	delay_ms(10);//�����꣬��Ҫ����һ�ᡣstm8�������ٶ�ûstm32�죬��һ�£��ô�����������ˢ��һ�¡�
	IIC_ReadBytes(GW_GRAY_ADDR_DEF, GW_GRAY_ANALOG_MODE, Result/*�򿪹�һ������������ݴ���Normalize��*/ , 8 );//����������ͨ��ģ������ȡһ��
	IIC_write_buff[0]=GW_GRAY_ANALOG_NORMALIZE;
	IIC_write_buff[1]=0x00;//ȫͨ���ر�
	IIC_WriteBytes(GW_GRAY_ADDR_DEF,GW_GRAY_ANALOG_NORMALIZE,&IIC_write_buff[1]/*����+����*/, 2 /*д����������*/);//Ϊ��while(1)ѭ����������Ȼ��Ҫ�ص���һ���ġ�
	return 1;
}

// I2C地址扫描函数 - 用于调试
unsigned char IIC_Scan_Address(void)
{
	unsigned char found_devices = 0;

	// 扫描I2C地址范围 0x08 到 0x77
	for(unsigned char addr = 0x08; addr <= 0x77; addr++)
	{
		// 尝试读取Ping寄存器来测试设备是否存在
		unsigned char test_result = IIC_ReadByte(addr, GW_GRAY_PING);
		if(test_result == GW_GRAY_PING_OK) // 如果Ping成功
		{
			found_devices++;
			// 这里可以通过串口输出找到的设备地址
		}
		delay_ms(1); // 短暂延时
	}

	return found_devices;
}

// 获取传感器固件版本 - 用于调试
unsigned char IIC_Get_Firmware_Version(void)
{
	return IIC_ReadByte(GW_GRAY_ADDR_DEF, GW_GRAY_FIRMWARE);
}

// 获取传感器错误状态 - 用于调试
unsigned char IIC_Get_Error_Status(void)
{
	return IIC_ReadByte(GW_GRAY_ADDR_DEF, GW_GRAY_ERROR);
}


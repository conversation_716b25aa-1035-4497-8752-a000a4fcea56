Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to pid.o(.text.PID_Init) for PID_Init
    empty.o(.text.main) refers to filter.o(.text.Kalman_Init) for Kalman_Init
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to hw_timer.o(.text.timer_init) for timer_init
    empty.o(.text.main) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    empty.o(.text.main) refers to empty.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    empty.o(.text.main) refers to bsp_motor_hallencoder.o(.text.encoder_init) for encoder_init
    empty.o(.text.main) refers to hw_lcd.o(.text.lcd_init) for lcd_init
    empty.o(.text.main) refers to printfa.o(i.__0sprintf) for sprintf
    empty.o(.text.main) refers to usart.o(.text.uart0_send_string) for uart0_send_string
    empty.o(.text.main) refers to memseta.o(.text) for __aeabi_memclr
    empty.o(.text.main) refers to hardware_iic.o(.text.Ping) for Ping
    empty.o(.text.main) refers to time.o(.text.delay_ms) for delay_ms
    empty.o(.text.main) refers to empty.o(.text.PID_operation) for PID_operation
    empty.o(.text.main) refers to empty.o(.bss.left_speed_pid) for left_speed_pid
    empty.o(.text.main) refers to empty.o(.bss.right_speed_pid) for right_speed_pid
    empty.o(.text.main) refers to empty.o(.bss.posion_kalman) for posion_kalman
    empty.o(.text.main) refers to empty.o(.bss.left_kalman) for left_kalman
    empty.o(.text.main) refers to empty.o(.bss.right_kalman) for right_kalman
    empty.o(.text.main) refers to empty.o(.bss.rx_buff) for rx_buff
    empty.o(.text.main) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.text.main) refers to empty.o(.bss.PID_flash_time) for PID_flash_time
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to empty.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to sensor.o(.text.UpdatePosition) for UpdatePosition
    empty.o(.text.PID_operation) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_L) for get_encoder_count_L
    empty.o(.text.PID_operation) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_R) for get_encoder_count_R
    empty.o(.text.PID_operation) refers to fflti.o(.text) for __aeabi_i2f
    empty.o(.text.PID_operation) refers to filter.o(.text.Kalman_Update) for Kalman_Update
    empty.o(.text.PID_operation) refers to pid.o(.text.PID_Calculate) for PID_Calculate
    empty.o(.text.PID_operation) refers to fadd.o(.text) for __aeabi_fadd
    empty.o(.text.PID_operation) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.PID_operation) refers to bsp_tb6612.o(.text.AB_Control) for AB_Control
    empty.o(.text.PID_operation) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.PID_operation) refers to printfa.o(i.__0printf) for printf
    empty.o(.text.PID_operation) refers to empty.o(.bss.left_kalman) for left_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.right_kalman) for right_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.posion_kalman) for posion_kalman
    empty.o(.text.PID_operation) refers to empty.o(.bss.posion_pid) for posion_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to empty.o(.bss.left_speed_pid) for left_speed_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.right_speed_pid) for right_speed_pid
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_left_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to empty.o(.bss.PID_operation.total_right_speed) for [Anonymous Symbol]
    empty.o(.text.PID_operation) refers to hw_timer.o(.bss.Num_L) for Num_L
    empty.o(.text.PID_operation) refers to hw_timer.o(.bss.Num_R) for Num_R
    empty.o(.text.PID_operation) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.PID_operation) refers to empty.o(.text.PID_operation) for [Anonymous Symbol]
    empty.o(.text.UART0_IRQHandler) refers to empty.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    empty.o(.text.UART0_IRQHandler) refers to empty.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    empty.o(.text.UART0_IRQHandler) refers to usart.o(.text.uart0_send_char) for uart0_send_char
    empty.o(.text.UART0_IRQHandler) refers to empty.o(.bss.uart_data) for uart_data
    empty.o(.ARM.exidx.text.UART0_IRQHandler) refers to empty.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to empty.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_UART_receiveData) refers to empty.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.data.TIMG0_IRQHandler.timer_count) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.led_flash_time) for led_flash_time
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.bmq_flash_time) for bmq_flash_time
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.PID_flash_time) for PID_flash_time
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to empty.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to empty.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to hw_timer.o(.text.TIMA0_IRQHandler) for TIMA0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for SYSCFG_DL_PWM_LED_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for SYSCFG_DL_TIMER_TICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) for SYSCFG_DL_SPI_LCD_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for DL_I2C_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_SPI_reset) for DL_SPI_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for DL_I2C_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_SPI_enablePower) for DL_SPI_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for DL_GPIO_initPeripheralAnalogFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for DL_GPIO_initPeripheralInputFunctionFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for DL_GPIO_enableHiZ
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for DL_GPIO_initDigitalOutputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for DL_GPIO_initDigitalInputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for DL_GPIO_setLowerPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for DL_GPIO_setUpperPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for DL_SYSCTL_enableMFCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for DL_I2C_setAnalogGlitchFilterPulseWidth
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for DL_I2C_enableAnalogGlitchFilter
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for DL_I2C_resetControllerTransfer
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for DL_I2C_setTimerPeriod
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for DL_I2C_setControllerTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for DL_I2C_setControllerRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for DL_I2C_enableControllerClockStretching
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for DL_I2C_enableController
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for DL_SPI_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to dl_spi.o(.text.DL_SPI_init) for DL_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) for DL_SPI_setBitRateSerialClockDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) for DL_SPI_setFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_enable) for DL_SPI_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.rodata.gSPI_LCD_config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for DL_SYSTICK_enable
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for DL_SPI_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for DL_SPI_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_reset) refers to ti_msp_dl_config.o(.text.DL_SPI_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enablePower) refers to ti_msp_dl_config.o(.text.DL_SPI_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setBitRateSerialClockDivider) refers to ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enable) refers to ti_msp_dl_config.o(.text.DL_SPI_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    led.o(.text.LED_flash) refers to led.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    led.o(.text.LED_flash) refers to time.o(.text.delay_ms) for delay_ms
    led.o(.text.LED_flash) refers to led.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    led.o(.ARM.exidx.text.LED_flash) refers to led.o(.text.LED_flash) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to led.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_setPins) refers to led.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    key.o(.text.KEY_control_LED) refers to key.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    key.o(.ARM.exidx.text.KEY_control_LED) refers to key.o(.text.KEY_control_LED) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_setPins) refers to key.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to key.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    key.o(.text.Get_KEY) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.text.Get_KEY) refers to time.o(.text.delay_ms) for delay_ms
    key.o(.ARM.exidx.text.Get_KEY) refers to key.o(.text.Get_KEY) for [Anonymous Symbol]
    usart.o(.text.uart0_send_char) refers to usart.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    usart.o(.text.uart0_send_char) refers to usart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    usart.o(.ARM.exidx.text.uart0_send_char) refers to usart.o(.text.uart0_send_char) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.DL_UART_isBusy) refers to usart.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.DL_UART_transmitData) refers to usart.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    usart.o(.text.uart0_send_string) refers to usart.o(.text.uart0_send_char) for uart0_send_char
    usart.o(.ARM.exidx.text.uart0_send_string) refers to usart.o(.text.uart0_send_string) for [Anonymous Symbol]
    usart.o(.text.fputc) refers to usart.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    usart.o(.text.fputc) refers to usart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    usart.o(.ARM.exidx.text.fputc) refers to usart.o(.text.fputc) for [Anonymous Symbol]
    pwm.o(.text.PWM_LED) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    pwm.o(.text.PWM_LED) refers to time.o(.text.delay_ms) for delay_ms
    pwm.o(.ARM.exidx.text.PWM_LED) refers to pwm.o(.text.PWM_LED) for [Anonymous Symbol]
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Start) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Start) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.ARM.exidx.text.IIC_Start) refers to iic.o(.text.IIC_Start) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_setPins) refers to iic.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to iic.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to iic.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Stop) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Stop) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.ARM.exidx.text.IIC_Stop) refers to iic.o(.text.IIC_Stop) for [Anonymous Symbol]
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Send_Ack) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Send_Ack) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.ARM.exidx.text.IIC_Send_Ack) refers to iic.o(.text.IIC_Send_Ack) for [Anonymous Symbol]
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Wait_Ack) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.IIC_Stop) for IIC_Stop
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Wait_Ack) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.ARM.exidx.text.IIC_Wait_Ack) refers to iic.o(.text.IIC_Wait_Ack) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to iic.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_GPIO_readPins) refers to iic.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    iic.o(.text.IIC_Send_Byte) refers to iic.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    iic.o(.text.IIC_Send_Byte) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Send_Byte) refers to iic.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    iic.o(.text.IIC_Send_Byte) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Send_Byte) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.ARM.exidx.text.IIC_Send_Byte) refers to iic.o(.text.IIC_Send_Byte) for [Anonymous Symbol]
    iic.o(.text.IIC_Read_Byte) refers to iic.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    iic.o(.text.IIC_Read_Byte) refers to iic.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    iic.o(.text.IIC_Read_Byte) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.text.IIC_Read_Byte) refers to iic.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    iic.o(.text.IIC_Read_Byte) refers to iic.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    iic.o(.ARM.exidx.text.IIC_Read_Byte) refers to iic.o(.text.IIC_Read_Byte) for [Anonymous Symbol]
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Start) for IIC_Start
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Send_Byte) for IIC_Send_Byte
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Wait_Ack) for IIC_Wait_Ack
    iic.o(.text.SHT20_Read) refers to printfa.o(i.__0printf) for printf
    iic.o(.text.SHT20_Read) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Read_Byte) for IIC_Read_Byte
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Send_Ack) for IIC_Send_Ack
    iic.o(.text.SHT20_Read) refers to iic.o(.text.IIC_Stop) for IIC_Stop
    iic.o(.text.SHT20_Read) refers to dflti.o(.text) for __aeabi_i2d
    iic.o(.text.SHT20_Read) refers to ddiv.o(.text) for __aeabi_ddiv
    iic.o(.text.SHT20_Read) refers to dmul.o(.text) for __aeabi_dmul
    iic.o(.text.SHT20_Read) refers to dadd.o(.text) for __aeabi_dadd
    iic.o(.text.SHT20_Read) refers to d2f.o(.text) for __aeabi_d2f
    iic.o(.text.SHT20_Read) refers to iic.o(.rodata.str1.1) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.SHT20_Read) refers to iic.o(.text.SHT20_Read) for [Anonymous Symbol]
    hw_lcd.o(.text.spi_write_bus) refers to hw_lcd.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    hw_lcd.o(.text.spi_write_bus) refers to hw_lcd.o(.text.DL_SPI_isBusy) for DL_SPI_isBusy
    hw_lcd.o(.ARM.exidx.text.spi_write_bus) refers to hw_lcd.o(.text.spi_write_bus) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_SPI_transmitData8) refers to hw_lcd.o(.text.DL_SPI_transmitData8) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_SPI_isBusy) refers to hw_lcd.o(.text.DL_SPI_isBusy) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.spi_write_bus) for spi_write_bus
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.ARM.exidx.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.LCD_Writ_Bus) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_lcd.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_DATA8) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA8) refers to hw_lcd.o(.text.LCD_WR_DATA8) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_DATA) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA) refers to hw_lcd.o(.text.LCD_WR_DATA) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.ARM.exidx.text.LCD_WR_REG) refers to hw_lcd.o(.text.LCD_WR_REG) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_WR_REG) for LCD_WR_REG
    hw_lcd.o(.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_Address_Set) for [Anonymous Symbol]
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.lcd_init) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.LCD_WR_REG) for LCD_WR_REG
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    hw_lcd.o(.ARM.exidx.text.lcd_init) refers to hw_lcd.o(.text.lcd_init) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_Fill) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_DrawPoint) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawLine) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.LCD_DrawLine) refers to hw_lcd.o(.text.LCD_DrawLine) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_DrawVerrticalLine) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawRectangle) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    hw_lcd.o(.ARM.exidx.text.LCD_DrawRectangle) refers to hw_lcd.o(.text.LCD_DrawRectangle) for [Anonymous Symbol]
    hw_lcd.o(.text.Draw_Circle) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.Draw_Circle) refers to hw_lcd.o(.text.Draw_Circle) for [Anonymous Symbol]
    hw_lcd.o(.text.Drawarc) refers to dfltui.o(.text) for __aeabi_ui2d
    hw_lcd.o(.text.Drawarc) refers to sqrt.o(i.sqrt) for sqrt
    hw_lcd.o(.text.Drawarc) refers to dfixi.o(.text) for __aeabi_d2iz
    hw_lcd.o(.text.Drawarc) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.Drawarc) refers to hw_lcd.o(.text.Drawarc) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.Drawarc) for Drawarc
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    hw_lcd.o(.ARM.exidx.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_ArcRect) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese12x12) for LCD_ShowChinese12x12
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese16x16) for LCD_ShowChinese16x16
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese24x24) for LCD_ShowChinese24x24
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese32x32) for LCD_ShowChinese32x32
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.rodata.tfont12) for tfont12
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_ShowChinese12x12) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.rodata.tfont16) for tfont16
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_ShowChinese16x16) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.rodata.tfont24) for tfont24
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_ShowChinese24x24) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.rodata.tfont32) for tfont32
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_ShowChinese32x32) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChar) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_3216) for ascii_3216
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_2412) for ascii_2412
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_1608) for ascii_1608
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_1206) for ascii_1206
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_ShowChar) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowString) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowString) refers to hw_lcd.o(.text.LCD_ShowString) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.mypow) refers to hw_lcd.o(.text.mypow) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.mypow) for mypow
    hw_lcd.o(.text.LCD_ShowIntNum) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    hw_lcd.o(.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.LCD_ShowIntNum) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to fmul.o(.text) for __aeabi_fmul
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to ffixui.o(.text) for __aeabi_f2uiz
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.mypow) for mypow
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.LCD_ShowFloatNum1) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    hw_lcd.o(.ARM.exidx.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_ShowPicture) for [Anonymous Symbol]
    oled.o(.text.disp_x_center) refers to hw_lcd.o(.text.LCD_ArcRect) for LCD_ArcRect
    oled.o(.text.disp_x_center) refers to hw_lcd.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    oled.o(.ARM.exidx.text.disp_x_center) refers to oled.o(.text.disp_x_center) for [Anonymous Symbol]
    oled.o(.text.disp_string_rect) refers to hw_lcd.o(.text.LCD_ArcRect) for LCD_ArcRect
    oled.o(.text.disp_string_rect) refers to hw_lcd.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    oled.o(.ARM.exidx.text.disp_string_rect) refers to oled.o(.text.disp_string_rect) for [Anonymous Symbol]
    oled.o(.text.disp_select_box) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    oled.o(.ARM.exidx.text.disp_select_box) refers to oled.o(.text.disp_select_box) for [Anonymous Symbol]
    oled.o(.text.ui_home_page) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.text.ui_home_page) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_x_center) for disp_x_center
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_string_rect) for disp_string_rect
    oled.o(.text.ui_home_page) refers to oled.o(.text.disp_select_box) for disp_select_box
    oled.o(.text.ui_home_page) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.text.ui_home_page) refers to oled.o(.rodata.str1.1) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.ui_home_page) refers to oled.o(.text.ui_home_page) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to oled.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_setPins) refers to oled.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_key.o(.text.key_scan) refers to hw_key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_key.o(.ARM.exidx.text.key_scan) refers to hw_key.o(.text.key_scan) for [Anonymous Symbol]
    hw_key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to hw_key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_Init) refers to pid.o(.text.PID_Init) for [Anonymous Symbol]
    pid.o(.text.PID_Calculate) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.PID_Calculate) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.text.PID_Calculate) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.text.PID_Calculate) refers to fdiv.o(.text) for __aeabi_fdiv
    pid.o(.ARM.exidx.text.PID_Calculate) refers to pid.o(.text.PID_Calculate) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    bsp_motor_hallencoder.o(.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_init) refers to bsp_motor_hallencoder.o(.text.encoder_init) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dflti.o(.text) for __aeabi_i2d
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dmul.o(.text) for __aeabi_dmul
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to dfixi.o(.text) for __aeabi_d2iz
    bsp_motor_hallencoder.o(.text.get_encoder_count_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_L) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dflti.o(.text) for __aeabi_i2d
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dmul.o(.text) for __aeabi_dmul
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to dfixi.o(.text) for __aeabi_d2iz
    bsp_motor_hallencoder.o(.text.get_encoder_count_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_R) refers to bsp_motor_hallencoder.o(.text.get_encoder_count_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_dir_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_L) refers to bsp_motor_hallencoder.o(.text.get_encoder_dir_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.get_encoder_dir_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_R) refers to bsp_motor_hallencoder.o(.text.get_encoder_dir_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_update_L) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_L) refers to bsp_motor_hallencoder.o(.text.encoder_update_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.encoder_update_R) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_R) refers to bsp_motor_hallencoder.o(.text.encoder_update_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_R) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.bss.motor_encoder_L) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_readPins) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    bsp_tb6612.o(.text.TB6612_Motor_Stop) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_tb6612.o(.ARM.exidx.text.TB6612_Motor_Stop) refers to bsp_tb6612.o(.text.TB6612_Motor_Stop) for [Anonymous Symbol]
    bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_setPins) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    bsp_tb6612.o(.text.AB_Control) refers to bsp_tb6612.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_tb6612.o(.text.AB_Control) refers to bsp_tb6612.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_tb6612.o(.text.AB_Control) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    bsp_tb6612.o(.ARM.exidx.text.AB_Control) refers to bsp_tb6612.o(.text.AB_Control) for [Anonymous Symbol]
    bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to bsp_tb6612.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_timer.o(.text.timer_init) refers to hw_timer.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    hw_timer.o(.text.timer_init) refers to hw_timer.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    hw_timer.o(.ARM.exidx.text.timer_init) refers to hw_timer.o(.text.timer_init) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to hw_timer.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to hw_timer.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    hw_timer.o(.text.TIMA0_IRQHandler) refers to hw_timer.o(.text.DL_Timer_getPendingInterrupt) for DL_Timer_getPendingInterrupt
    hw_timer.o(.text.TIMA0_IRQHandler) refers to bsp_motor_hallencoder.o(.text.encoder_update_L) for encoder_update_L
    hw_timer.o(.text.TIMA0_IRQHandler) refers to bsp_motor_hallencoder.o(.text.encoder_update_R) for encoder_update_R
    hw_timer.o(.ARM.exidx.text.TIMA0_IRQHandler) refers to hw_timer.o(.text.TIMA0_IRQHandler) for [Anonymous Symbol]
    hw_timer.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt) refers to hw_timer.o(.text.DL_Timer_getPendingInterrupt) for [Anonymous Symbol]
    filter.o(.ARM.exidx.text.Kalman_Init) refers to filter.o(.text.Kalman_Init) for [Anonymous Symbol]
    filter.o(.text.Kalman_Update) refers to fadd.o(.text) for __aeabi_fadd
    filter.o(.text.Kalman_Update) refers to fdiv.o(.text) for __aeabi_fdiv
    filter.o(.text.Kalman_Update) refers to fmul.o(.text) for __aeabi_fmul
    filter.o(.ARM.exidx.text.Kalman_Update) refers to filter.o(.text.Kalman_Update) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadByte) refers to hw_i2c.o(.text.hardware_IIC_ReadByte) for hardware_IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to hardware_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadBytes) refers to hw_i2c.o(.text.hardware_IIC_ReadBytes) for hardware_IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to hardware_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteByte) refers to hw_i2c.o(.text.hardware_IIC_WirteByte) for hardware_IIC_WirteByte
    hardware_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to hardware_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteBytes) refers to hw_i2c.o(.text.hardware_IIC_WirteBytes) for hardware_IIC_WirteBytes
    hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to hardware_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.Ping) refers to hardware_iic.o(.text.IIC_ReadByte) for IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.Ping) refers to hardware_iic.o(.text.Ping) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_ReadByte) for IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_Get_Digtal) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_Get_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_Get_Single_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(.text.IIC_Get_Normalize) refers to time.o(.text.delay_ms) for delay_ms
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.bss.IIC_write_buff) for IIC_write_buff
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_Get_Normalize) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteByte) refers to hw_i2c.o(.text.hardware_IIC_WirteByte) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_I2C_getControllerStatus) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for [Anonymous Symbol]
    hw_i2c.o(.text.DL_I2C_startControllerTransfer) refers to hw_i2c.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    hw_i2c.o(.ARM.exidx.text.DL_I2C_startControllerTransfer) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to memcpya.o(.text) for __aeabi_memcpy
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteBytes) refers to hw_i2c.o(.text.hardware_IIC_WirteBytes) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadByte) refers to hw_i2c.o(.text.hardware_IIC_ReadByte) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_I2C_receiveControllerData) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for [Anonymous Symbol]
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    hw_i2c.o(.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadBytes) refers to hw_i2c.o(.text.hardware_IIC_ReadBytes) for [Anonymous Symbol]
    hw_i2c.o(.ARM.exidx.text.DL_Common_updateReg) refers to hw_i2c.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_us) refers to time.o(.text.delay_us) for [Anonymous Symbol]
    time.o(.text.delay_ms) refers to time.o(.text.delay_us) for delay_us
    time.o(.ARM.exidx.text.delay_ms) refers to time.o(.text.delay_ms) for [Anonymous Symbol]
    sensor.o(.text.CalculatePositionFromDigital) refers to fflti.o(.text) for __aeabi_i2f
    sensor.o(.text.CalculatePositionFromDigital) refers to fadd.o(.text) for __aeabi_fadd
    sensor.o(.text.CalculatePositionFromDigital) refers to fdiv.o(.text) for __aeabi_fdiv
    sensor.o(.text.CalculatePositionFromDigital) refers to sensor.o(.rodata.position_weights) for [Anonymous Symbol]
    sensor.o(.ARM.exidx.text.CalculatePositionFromDigital) refers to sensor.o(.text.CalculatePositionFromDigital) for [Anonymous Symbol]
    sensor.o(.text.UpdatePosition) refers to hardware_iic.o(.text.IIC_Get_Digtal) for IIC_Get_Digtal
    sensor.o(.text.UpdatePosition) refers to sensor.o(.text.CalculatePositionFromDigital) for CalculatePositionFromDigital
    sensor.o(.ARM.exidx.text.UpdatePosition) refers to sensor.o(.text.UpdatePosition) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing empty.o(.ARM.exidx.text.PID_operation), (8 bytes).
    Removing empty.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing empty.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing empty.o(.bss.delay_times), (4 bytes).
    Removing empty.o(.bss.Anolog), (8 bytes).
    Removing empty.o(.bss.Normal), (8 bytes).
    Removing empty.o(.bss.temporary_str), (40 bytes).
    Removing empty.o(.bss.llll), (4 bytes).
    Removing empty.o(.bss.rrrr), (4 bytes).
    Removing empty.o(.bss.Digtal), (1 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing startup_mspm0g350x_uvision.o(HEAP), (1024 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_LCD_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (128 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (136 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setBitRateSerialClockDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.text.LED_flash), (52 bytes).
    Removing led.o(.ARM.exidx.text.LED_flash), (8 bytes).
    Removing led.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing led.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.text.KEY_control_LED), (48 bytes).
    Removing key.o(.ARM.exidx.text.KEY_control_LED), (8 bytes).
    Removing key.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing key.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing key.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing key.o(.text.Get_KEY), (80 bytes).
    Removing key.o(.ARM.exidx.text.Get_KEY), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing usart.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing usart.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing usart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing pwm.o(.text), (0 bytes).
    Removing pwm.o(.text.PWM_LED), (120 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_LED), (8 bytes).
    Removing iic.o(.text), (0 bytes).
    Removing iic.o(.text.IIC_Start), (104 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing iic.o(.text.DL_GPIO_initDigitalOutput), (24 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing iic.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing iic.o(.text.DL_GPIO_enableOutput), (24 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing iic.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing iic.o(.text.IIC_Stop), (88 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing iic.o(.text.IIC_Send_Ack), (136 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Send_Ack), (8 bytes).
    Removing iic.o(.text.IIC_Wait_Ack), (196 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Wait_Ack), (8 bytes).
    Removing iic.o(.text.DL_GPIO_initDigitalInput), (28 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing iic.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing iic.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing iic.o(.text.IIC_Send_Byte), (160 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Send_Byte), (8 bytes).
    Removing iic.o(.text.IIC_Read_Byte), (156 bytes).
    Removing iic.o(.ARM.exidx.text.IIC_Read_Byte), (8 bytes).
    Removing iic.o(.text.SHT20_Read), (248 bytes).
    Removing iic.o(.ARM.exidx.text.SHT20_Read), (8 bytes).
    Removing iic.o(.rodata.str1.1), (22 bytes).
    Removing hw_lcd.o(.text), (0 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.spi_write_bus), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_SPI_transmitData8), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_SPI_isBusy), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Writ_Bus), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA8), (8 bytes).
    Removing hw_lcd.o(.text.LCD_WR_DATA), (28 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_REG), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Address_Set), (62 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Address_Set), (8 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.lcd_init), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Fill), (92 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Fill), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawPoint), (32 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawPoint), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawLine), (268 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawLine), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawVerrticalLine), (66 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawVerrticalLine), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawRectangle), (82 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawRectangle), (8 bytes).
    Removing hw_lcd.o(.text.Draw_Circle), (220 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.Draw_Circle), (8 bytes).
    Removing hw_lcd.o(.text.Drawarc), (338 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.Drawarc), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ArcRect), (330 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ArcRect), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese), (296 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese12x12), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese16x16), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese24x24), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese32x32), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChar), (476 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChar), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowString), (98 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowString), (8 bytes).
    Removing hw_lcd.o(.text.mypow), (48 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.mypow), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowIntNum), (244 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowIntNum), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowFloatNum1), (252 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowPicture), (128 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowPicture), (8 bytes).
    Removing hw_lcd.o(.rodata.ascii_1206), (1140 bytes).
    Removing hw_lcd.o(.rodata.ascii_1608), (1520 bytes).
    Removing hw_lcd.o(.rodata.ascii_2412), (4560 bytes).
    Removing hw_lcd.o(.rodata.ascii_3216), (6080 bytes).
    Removing hw_lcd.o(.rodata.tfont12), (130 bytes).
    Removing hw_lcd.o(.rodata.tfont16), (374 bytes).
    Removing hw_lcd.o(.rodata.tfont24), (222 bytes).
    Removing hw_lcd.o(.rodata.tfont32), (130 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.disp_x_center), (100 bytes).
    Removing oled.o(.ARM.exidx.text.disp_x_center), (8 bytes).
    Removing oled.o(.text.disp_string_rect), (136 bytes).
    Removing oled.o(.ARM.exidx.text.disp_string_rect), (8 bytes).
    Removing oled.o(.text.disp_select_box), (234 bytes).
    Removing oled.o(.ARM.exidx.text.disp_select_box), (8 bytes).
    Removing oled.o(.text.ui_home_page), (216 bytes).
    Removing oled.o(.ARM.exidx.text.ui_home_page), (8 bytes).
    Removing oled.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing oled.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing oled.o(.rodata.str1.1), (37 bytes).
    Removing hw_key.o(.text), (0 bytes).
    Removing hw_key.o(.text.key_scan), (140 bytes).
    Removing hw_key.o(.ARM.exidx.text.key_scan), (8 bytes).
    Removing hw_key.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing hw_key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Calculate), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text), (0 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_count_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text.get_encoder_dir_L), (12 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.text.get_encoder_dir_R), (12 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.get_encoder_dir_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_L), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.encoder_update_R), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing bsp_motor_hallencoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing bsp_tb6612.o(.text), (0 bytes).
    Removing bsp_tb6612.o(.text.TB6612_Motor_Stop), (56 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.TB6612_Motor_Stop), (8 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.AB_Control), (8 bytes).
    Removing bsp_tb6612.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_timer.o(.text), (0 bytes).
    Removing hw_timer.o(.ARM.exidx.text.timer_init), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.TIMA0_IRQHandler), (8 bytes).
    Removing hw_timer.o(.ARM.exidx.text.DL_Timer_getPendingInterrupt), (8 bytes).
    Removing filter.o(.text), (0 bytes).
    Removing filter.o(.ARM.exidx.text.Kalman_Init), (8 bytes).
    Removing filter.o(.ARM.exidx.text.Kalman_Update), (8 bytes).
    Removing hardware_iic.o(.text), (0 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_ReadBytes), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteByte), (36 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteBytes), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Anolog), (52 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Single_Anolog), (34 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Normalize), (84 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Normalize), (8 bytes).
    Removing hardware_iic.o(.bss.IIC_write_buff), (10 bytes).
    Removing hw_i2c.o(.text), (0 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_WirteByte), (120 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteByte), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_getControllerStatus), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_startControllerTransfer), (8 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_WirteBytes), (196 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_WirteBytes), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadByte), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_I2C_receiveControllerData), (8 bytes).
    Removing hw_i2c.o(.text.hardware_IIC_ReadBytes), (220 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.hardware_IIC_ReadBytes), (8 bytes).
    Removing hw_i2c.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing time.o(.text), (0 bytes).
    Removing time.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing time.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing sensor.o(.text), (0 bytes).
    Removing sensor.o(.ARM.exidx.text.CalculatePositionFromDigital), (8 bytes).
    Removing sensor.o(.ARM.exidx.text.UpdatePosition), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (80 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).
    Removing dfltui.o(.text), (28 bytes).
    Removing ffixui.o(.text), (40 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing dsqrt.o(.text), (162 bytes).

508 unused section(s) (total 31518 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmple.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    Filter.c                                 0x00000000   Number         0  filter.o ABSOLUTE
    KEY.c                                    0x00000000   Number         0  key.o ABSOLUTE
    LED.c                                    0x00000000   Number         0  led.o ABSOLUTE
    OLED.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    PID.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    PWM.c                                    0x00000000   Number         0  pwm.o ABSOLUTE
    Sensor.c                                 0x00000000   Number         0  sensor.o ABSOLUTE
    Time.c                                   0x00000000   Number         0  time.o ABSOLUTE
    USART.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    bsp_motor_hallencoder.c                  0x00000000   Number         0  bsp_motor_hallencoder.o ABSOLUTE
    bsp_tb6612.c                             0x00000000   Number         0  bsp_tb6612.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    hardware_iic.c                           0x00000000   Number         0  hardware_iic.o ABSOLUTE
    hw_i2c.c                                 0x00000000   Number         0  hw_i2c.o ABSOLUTE
    hw_key.c                                 0x00000000   Number         0  hw_key.o ABSOLUTE
    hw_lcd.c                                 0x00000000   Number         0  hw_lcd.o ABSOLUTE
    hw_timer.c                               0x00000000   Number         0  hw_timer.o ABSOLUTE
    iic.c                                    0x00000000   Number         0  iic.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  memseta.o(.text)
    .text                                    0x0000010c   Section        0  fadd.o(.text)
    .text                                    0x000001be   Section        0  fmul.o(.text)
    .text                                    0x00000238   Section        0  fdiv.o(.text)
    .text                                    0x000002b4   Section        0  dadd.o(.text)
    .text                                    0x00000418   Section        0  dmul.o(.text)
    .text                                    0x000004e8   Section        0  ddiv.o(.text)
    .text                                    0x000005d8   Section        0  fcmple.o(.text)
    .text                                    0x000005f4   Section        0  fflti.o(.text)
    .text                                    0x0000060c   Section        0  dflti.o(.text)
    .text                                    0x00000634   Section        0  ffixi.o(.text)
    .text                                    0x00000668   Section        0  dfixi.o(.text)
    .text                                    0x000006b0   Section        0  f2d.o(.text)
    .text                                    0x000006d8   Section        0  uidiv_div0.o(.text)
    .text                                    0x00000716   Section        0  uldiv.o(.text)
    .text                                    0x00000776   Section        0  llshl.o(.text)
    .text                                    0x00000796   Section        0  llushr.o(.text)
    .text                                    0x000007b8   Section        0  llsshr.o(.text)
    .text                                    0x000007de   Section        0  iusefp.o(.text)
    .text                                    0x000007de   Section        0  fepilogue.o(.text)
    .text                                    0x00000860   Section        0  depilogue.o(.text)
    .text                                    0x00000920   Section        0  dfixul.o(.text)
    .text                                    0x00000960   Section       40  cdrcmple.o(.text)
    .text                                    0x00000988   Section       48  init.o(.text)
    [Anonymous Symbol]                       0x000009b8   Section        0  bsp_tb6612.o(.text.AB_Control)
    __arm_cp.2_0                             0x00000a60   Number         4  bsp_tb6612.o(.text.AB_Control)
    __arm_cp.2_1                             0x00000a64   Number         4  bsp_tb6612.o(.text.AB_Control)
    [Anonymous Symbol]                       0x00000a68   Section        0  sensor.o(.text.CalculatePositionFromDigital)
    __arm_cp.0_0                             0x00000ae4   Number         4  sensor.o(.text.CalculatePositionFromDigital)
    [Anonymous Symbol]                       0x00000ae8   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x00000af3   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000af2   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x00000b1b   Thumb Code    40  hw_i2c.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000b1a   Section        0  hw_i2c.o(.text.DL_Common_updateReg)
    DL_GPIO_clearInterruptStatus             0x00000b45   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000b44   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x00000b5d   Thumb Code    24  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000b5c   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.12_0                            0x00000b74   Number         4  bsp_motor_hallencoder.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x00000b79   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000b78   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00000b8d   Thumb Code    20  hw_lcd.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000b8c   Section        0  hw_lcd.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00000ba1   Thumb Code    20  bsp_tb6612.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000ba0   Section        0  bsp_tb6612.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableHiZ                        0x00000bb5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    [Anonymous Symbol]                       0x00000bb4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    DL_GPIO_enableInterrupt                  0x00000bcd   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x00000bcc   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.38_0                            0x00000be4   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x00000be9   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00000be8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.26_0                            0x00000bfc   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00000c01   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00000c00   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x00000c15   Thumb Code    20  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x00000c14   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.10_0                            0x00000c28   Number         4  bsp_motor_hallencoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInputFeatures         0x00000c2d   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    [Anonymous Symbol]                       0x00000c2c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    __arm_cp.31_0                            0x00000c58   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    DL_GPIO_initDigitalOutput                0x00000c5d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x00000c5c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutputFeatures        0x00000c71   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    [Anonymous Symbol]                       0x00000c70   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    DL_GPIO_initPeripheralAnalogFunction     0x00000c9d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    [Anonymous Symbol]                       0x00000c9c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    DL_GPIO_initPeripheralInputFunction      0x00000cb1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00000cb0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralInputFunctionFeatures 0x00000cc9   Thumb Code    52  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    [Anonymous Symbol]                       0x00000cc8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    __arm_cp.27_0                            0x00000cfc   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    DL_GPIO_initPeripheralOutputFunction     0x00000d01   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x00000d00   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.25_0                            0x00000d18   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x00000d1d   Thumb Code    22  bsp_motor_hallencoder.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000d1c   Section        0  bsp_motor_hallencoder.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00000d35   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00000d34   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setLowerPinsPolarity             0x00000d45   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    [Anonymous Symbol]                       0x00000d44   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    __arm_cp.35_0                            0x00000d5c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    DL_GPIO_setPins                          0x00000d61   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000d60   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00000d75   Thumb Code    20  hw_lcd.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000d74   Section        0  hw_lcd.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00000d89   Thumb Code    20  bsp_tb6612.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000d88   Section        0  bsp_tb6612.o(.text.DL_GPIO_setPins)
    __arm_cp.1_0                             0x00000d9c   Number         4  bsp_tb6612.o(.text.DL_GPIO_setPins)
    DL_GPIO_setUpperPinsPolarity             0x00000da1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    [Anonymous Symbol]                       0x00000da0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    DL_I2C_enableAnalogGlitchFilter          0x00000db9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    [Anonymous Symbol]                       0x00000db8   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    DL_I2C_enableController                  0x00000dd1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    [Anonymous Symbol]                       0x00000dd0   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    DL_I2C_enableControllerClockStretching   0x00000de5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    [Anonymous Symbol]                       0x00000de4   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    __arm_cp.57_0                            0x00000df8   Number         4  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    DL_I2C_enablePower                       0x00000dfd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    [Anonymous Symbol]                       0x00000dfc   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    __arm_cp.21_0                            0x00000e10   Number         4  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    [Anonymous Symbol]                       0x00000e14   Section        0  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    [Anonymous Symbol]                       0x00000e40   Section        0  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    DL_I2C_getControllerStatus               0x00000e65   Thumb Code    16  hw_i2c.o(.text.DL_I2C_getControllerStatus)
    [Anonymous Symbol]                       0x00000e64   Section        0  hw_i2c.o(.text.DL_I2C_getControllerStatus)
    __arm_cp.1_0                             0x00000e74   Number         4  hw_i2c.o(.text.DL_I2C_getControllerStatus)
    DL_I2C_receiveControllerData             0x00000e79   Thumb Code    16  hw_i2c.o(.text.DL_I2C_receiveControllerData)
    [Anonymous Symbol]                       0x00000e78   Section        0  hw_i2c.o(.text.DL_I2C_receiveControllerData)
    __arm_cp.5_0                             0x00000e88   Number         4  hw_i2c.o(.text.DL_I2C_receiveControllerData)
    DL_I2C_reset                             0x00000e8d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_reset)
    [Anonymous Symbol]                       0x00000e8c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_reset)
    DL_I2C_resetControllerTransfer           0x00000e9d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    [Anonymous Symbol]                       0x00000e9c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    DL_I2C_setAnalogGlitchFilterPulseWidth   0x00000ead   Thumb Code    38  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00000eac   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00000ed2   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_I2C_setControllerRXFIFOThreshold      0x00000ef9   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    [Anonymous Symbol]                       0x00000ef8   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    DL_I2C_setControllerTXFIFOThreshold      0x00000f1d   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    [Anonymous Symbol]                       0x00000f1c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    __arm_cp.55_0                            0x00000f40   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    DL_I2C_setTimerPeriod                    0x00000f45   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    [Anonymous Symbol]                       0x00000f44   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    __arm_cp.54_0                            0x00000f58   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    DL_I2C_startControllerTransfer           0x00000f5d   Thumb Code    64  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    [Anonymous Symbol]                       0x00000f5c   Section        0  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    __arm_cp.2_0                             0x00000f9c   Number         4  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    __arm_cp.2_1                             0x00000fa0   Number         4  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    __arm_cp.2_2                             0x00000fa4   Number         4  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    __arm_cp.2_3                             0x00000fa8   Number         4  hw_i2c.o(.text.DL_I2C_startControllerTransfer)
    DL_SPI_enable                            0x00000fad   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SPI_enable)
    [Anonymous Symbol]                       0x00000fac   Section        0  ti_msp_dl_config.o(.text.DL_SPI_enable)
    __arm_cp.65_0                            0x00000fc0   Number         4  ti_msp_dl_config.o(.text.DL_SPI_enable)
    DL_SPI_enablePower                       0x00000fc5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    [Anonymous Symbol]                       0x00000fc4   Section        0  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    __arm_cp.23_0                            0x00000fd8   Number         4  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    [Anonymous Symbol]                       0x00000fdc   Section        0  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_0                             0x00001018   Number         4  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_1                             0x0000101c   Number         4  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_isBusy                            0x00001021   Thumb Code    20  hw_lcd.o(.text.DL_SPI_isBusy)
    [Anonymous Symbol]                       0x00001020   Section        0  hw_lcd.o(.text.DL_SPI_isBusy)
    __arm_cp.2_0                             0x00001034   Number         4  hw_lcd.o(.text.DL_SPI_isBusy)
    DL_SPI_reset                             0x00001039   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SPI_reset)
    [Anonymous Symbol]                       0x00001038   Section        0  ti_msp_dl_config.o(.text.DL_SPI_reset)
    __arm_cp.18_0                            0x00001048   Number         4  ti_msp_dl_config.o(.text.DL_SPI_reset)
    __arm_cp.18_1                            0x0000104c   Number         4  ti_msp_dl_config.o(.text.DL_SPI_reset)
    DL_SPI_setBitRateSerialClockDivider      0x00001051   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    [Anonymous Symbol]                       0x00001050   Section        0  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    __arm_cp.63_1                            0x0000106c   Number         4  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    [Anonymous Symbol]                       0x00001070   Section        0  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SPI_setFIFOThreshold                  0x00001085   Thumb Code    44  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    [Anonymous Symbol]                       0x00001084   Section        0  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    __arm_cp.64_0                            0x000010b0   Number         4  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    DL_SPI_transmitData8                     0x000010b5   Thumb Code    22  hw_lcd.o(.text.DL_SPI_transmitData8)
    [Anonymous Symbol]                       0x000010b4   Section        0  hw_lcd.o(.text.DL_SPI_transmitData8)
    [Anonymous Symbol]                       0x000010cc   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00001180   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00001184   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00001188   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x0000118d   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x0000118c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00001199   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00001198   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.43_0                            0x000011a8   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_enableMFCLK                    0x000011ad   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    [Anonymous Symbol]                       0x000011ac   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    DL_SYSCTL_setBORThreshold                0x000011bd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x000011bc   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.39_0                            0x000011d0   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setFlashWaitState              0x000011d5   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x000011d4   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x000011f0   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x0000123c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_setSYSOSCFreq                  0x00001241   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x00001240   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.41_0                            0x00001258   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSCTL_setULPCLKDivider               0x0000125d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x0000125c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00001274   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00001294   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00001298   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_SYSTICK_enable                        0x0000129d   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    [Anonymous Symbol]                       0x0000129c   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    DL_SYSTICK_init                          0x000012a9   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x000012a8   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.66_0                            0x000012c4   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.66_1                            0x000012c8   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.66_2                            0x000012cc   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    DL_Timer_enableClock                     0x000012d1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x000012d0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    __arm_cp.48_0                            0x000012e0   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enableInterrupt                 0x000012e5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    [Anonymous Symbol]                       0x000012e4   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    DL_Timer_enablePower                     0x000012fd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x000012fc   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    __arm_cp.20_0                            0x00001310   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    DL_Timer_getPendingInterrupt             0x00001315   Thumb Code    18  empty.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001314   Section        0  empty.o(.text.DL_Timer_getPendingInterrupt)
    DL_Timer_getPendingInterrupt             0x00001327   Thumb Code    18  hw_timer.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001326   Section        0  hw_timer.o(.text.DL_Timer_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001338   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x0000142c   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x00001430   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x00001434   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x00001438   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x0000143c   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x00001440   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x00001520   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x00001524   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x00001528   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_reset                           0x0000152d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x0000152c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x0000153d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x0000153c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00001550   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x00001568   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x0000156c   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00001580   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00001584   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00001590   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00001594   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x000015ac   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterControl               0x000015b1   Thumb Code    52  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    [Anonymous Symbol]                       0x000015b0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.47_0                            0x000015e4   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.47_1                            0x000015e8   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    DL_UART_enable                           0x000015ed   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x000015ec   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableInterrupt                  0x00001605   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x00001604   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.61_0                            0x0000161c   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enablePower                      0x00001621   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x00001620   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.22_0                            0x00001634   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x00001639   Thumb Code    18  empty.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001638   Section        0  empty.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x0000164c   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x0000168c   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00001690   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_isBusy                           0x00001695   Thumb Code    20  usart.o(.text.DL_UART_isBusy)
    [Anonymous Symbol]                       0x00001694   Section        0  usart.o(.text.DL_UART_isBusy)
    __arm_cp.1_0                             0x000016a8   Number         4  usart.o(.text.DL_UART_isBusy)
    DL_UART_receiveData                      0x000016ad   Thumb Code    16  empty.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x000016ac   Section        0  empty.o(.text.DL_UART_receiveData)
    __arm_cp.6_0                             0x000016bc   Number         4  empty.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x000016c1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x000016c0   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.17_0                            0x000016d0   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.17_1                            0x000016d4   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x000016d9   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x000016d8   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.60_0                            0x00001714   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.60_1                            0x00001718   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.60_2                            0x0000171c   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.60_3                            0x00001720   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001724   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x00001737   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x00001736   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_transmitData                     0x00001755   Thumb Code    22  usart.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x00001754   Section        0  usart.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x0000176c   Section        0  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_1                             0x00001890   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_2                             0x00001894   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.9_3                             0x00001898   Number         4  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x0000189c   Section        0  hardware_iic.o(.text.IIC_Get_Digtal)
    [Anonymous Symbol]                       0x000018b4   Section        0  hardware_iic.o(.text.IIC_ReadByte)
    [Anonymous Symbol]                       0x000018d0   Section        0  filter.o(.text.Kalman_Init)
    [Anonymous Symbol]                       0x000018fa   Section        0  filter.o(.text.Kalman_Update)
    [Anonymous Symbol]                       0x00001966   Section        0  hw_lcd.o(.text.LCD_WR_DATA8)
    [Anonymous Symbol]                       0x0000197c   Section        0  hw_lcd.o(.text.LCD_WR_REG)
    [Anonymous Symbol]                       0x000019a8   Section        0  hw_lcd.o(.text.LCD_Writ_Bus)
    __arm_cp.3_0                             0x000019d4   Number         4  hw_lcd.o(.text.LCD_Writ_Bus)
    [Anonymous Symbol]                       0x000019d8   Section        0  pid.o(.text.PID_Calculate)
    __arm_cp.1_0                             0x00001a8c   Number         4  pid.o(.text.PID_Calculate)
    [Anonymous Symbol]                       0x00001a90   Section        0  pid.o(.text.PID_Init)
    [Anonymous Symbol]                       0x00001abc   Section        0  empty.o(.text.PID_operation)
    __arm_cp.3_0                             0x00001bd8   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_1                             0x00001bdc   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_2                             0x00001be0   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_3                             0x00001be4   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_4                             0x00001be8   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_5                             0x00001bec   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_6                             0x00001bf0   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_7                             0x00001bf4   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_8                             0x00001bf8   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_9                             0x00001bfc   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_10                            0x00001c00   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_11                            0x00001c04   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_12                            0x00001c08   Number         4  empty.o(.text.PID_operation)
    __arm_cp.3_13                            0x00001c0c   Number         4  empty.o(.text.PID_operation)
    [Anonymous Symbol]                       0x00001c10   Section        0  hardware_iic.o(.text.Ping)
    [Anonymous Symbol]                       0x00001c44   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00001e0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00001e10   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00001e14   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00001e18   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00001e1c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00001e20   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00001e24   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00001e28   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.8_0                             0x00001e78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x00001e7c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.5_0                             0x00001f00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.5_2                             0x00001f04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00001f08   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_0                             0x00001f68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_2                             0x00001f6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    [Anonymous Symbol]                       0x00001f70   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    __arm_cp.10_0                            0x00001fa4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    __arm_cp.10_2                            0x00001fa8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    [Anonymous Symbol]                       0x00001fac   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00002000   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00002004   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00002014   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_0                             0x0000203c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_2                             0x00002040   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00002044   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_0                             0x00002074   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_2                             0x00002078   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    [Anonymous Symbol]                       0x0000207c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.9_0                             0x000020b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.9_2                             0x000020bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x000020c0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00002108   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x0000210c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x00002110   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_3                             0x00002114   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00002118   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x000021a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x000021a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x000021ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x000021b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x000021b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x000021b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x000021bc   Section        0  hw_timer.o(.text.TIMA0_IRQHandler)
    __arm_cp.3_0                             0x000021d8   Number         4  hw_timer.o(.text.TIMA0_IRQHandler)
    [Anonymous Symbol]                       0x000021dc   Section        0  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_0                             0x00002268   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_1                             0x0000226c   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_2                             0x00002270   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_3                             0x00002274   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_4                             0x00002278   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_5                             0x0000227c   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_6                             0x00002280   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_7                             0x00002284   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_8                             0x00002288   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.7_9                             0x0000228c   Number         4  empty.o(.text.TIMG0_IRQHandler)
    [Anonymous Symbol]                       0x00002290   Section        0  empty.o(.text.UART0_IRQHandler)
    __arm_cp.4_0                             0x000022b8   Number         4  empty.o(.text.UART0_IRQHandler)
    __arm_cp.4_1                             0x000022bc   Number         4  empty.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x000022c0   Section        0  sensor.o(.text.UpdatePosition)
    __NVIC_ClearPendingIRQ                   0x000022dd   Thumb Code    40  empty.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x000022dc   Section        0  empty.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_ClearPendingIRQ                   0x00002305   Thumb Code    40  bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00002304   Section        0  bsp_motor_hallencoder.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_ClearPendingIRQ                   0x0000232d   Thumb Code    40  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x0000232c   Section        0  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.1_0                             0x00002354   Number         4  hw_timer.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x00002359   Thumb Code    40  empty.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00002358   Section        0  empty.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x00002381   Thumb Code    40  bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00002380   Section        0  bsp_motor_hallencoder.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x000023a9   Thumb Code    40  hw_timer.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x000023a8   Section        0  hw_timer.o(.text.__NVIC_EnableIRQ)
    __arm_cp.2_0                             0x000023d0   Number         4  hw_timer.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x000023d5   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x000023d4   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.46_0                            0x00002450   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.46_1                            0x00002454   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00002458   Section        0  time.o(.text.delay_ms)
    [Anonymous Symbol]                       0x00002470   Section        0  time.o(.text.delay_us)
    __arm_cp.0_0                             0x000024dc   Number         4  time.o(.text.delay_us)
    __arm_cp.0_1                             0x000024e0   Number         4  time.o(.text.delay_us)
    [Anonymous Symbol]                       0x000024e4   Section        0  bsp_motor_hallencoder.o(.text.encoder_init)
    [Anonymous Symbol]                       0x000024fc   Section        0  bsp_motor_hallencoder.o(.text.encoder_update_L)
    [Anonymous Symbol]                       0x00002514   Section        0  bsp_motor_hallencoder.o(.text.encoder_update_R)
    [Anonymous Symbol]                       0x0000252c   Section        0  usart.o(.text.fputc)
    __arm_cp.4_0                             0x00002554   Number         4  usart.o(.text.fputc)
    [Anonymous Symbol]                       0x00002558   Section        0  bsp_motor_hallencoder.o(.text.get_encoder_count_L)
    __arm_cp.3_0                             0x00002570   Number         4  bsp_motor_hallencoder.o(.text.get_encoder_count_L)
    [Anonymous Symbol]                       0x00002574   Section        0  bsp_motor_hallencoder.o(.text.get_encoder_count_R)
    __arm_cp.4_0                             0x0000258c   Number         4  bsp_motor_hallencoder.o(.text.get_encoder_count_R)
    __arm_cp.4_1                             0x00002590   Number         4  bsp_motor_hallencoder.o(.text.get_encoder_count_R)
    [Anonymous Symbol]                       0x00002594   Section        0  hw_i2c.o(.text.hardware_IIC_ReadByte)
    __arm_cp.4_0                             0x00002640   Number         4  hw_i2c.o(.text.hardware_IIC_ReadByte)
    [Anonymous Symbol]                       0x00002644   Section        0  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_0                            0x00002804   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_1                            0x00002808   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_2                            0x0000280c   Number         4  hw_lcd.o(.text.lcd_init)
    __arm_cp.10_3                            0x00002810   Number         4  hw_lcd.o(.text.lcd_init)
    [Anonymous Symbol]                       0x00002814   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00002928   Number         4  empty.o(.text.main)
    __arm_cp.0_1                             0x0000292c   Number         4  empty.o(.text.main)
    __arm_cp.0_2                             0x00002930   Number         4  empty.o(.text.main)
    __arm_cp.0_3                             0x00002934   Number         4  empty.o(.text.main)
    __arm_cp.0_4                             0x00002938   Number         4  empty.o(.text.main)
    __arm_cp.0_5                             0x0000293c   Number         4  empty.o(.text.main)
    __arm_cp.0_6                             0x00002940   Number         4  empty.o(.text.main)
    __arm_cp.0_7                             0x00002944   Number         4  empty.o(.text.main)
    __arm_cp.0_8                             0x00002948   Number         4  empty.o(.text.main)
    __arm_cp.0_9                             0x0000294c   Number         4  empty.o(.text.main)
    __arm_cp.0_10                            0x00002950   Number         4  empty.o(.text.main)
    __arm_cp.0_11                            0x00002954   Number         4  empty.o(.text.main)
    __arm_cp.0_12                            0x00002958   Number         4  empty.o(.text.main)
    __arm_cp.0_13                            0x0000295c   Number         4  empty.o(.text.main)
    __arm_cp.0_14                            0x00002960   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00002964   Section        0  hw_lcd.o(.text.spi_write_bus)
    __arm_cp.0_0                             0x0000298c   Number         4  hw_lcd.o(.text.spi_write_bus)
    [Anonymous Symbol]                       0x00002990   Section        0  hw_timer.o(.text.timer_init)
    [Anonymous Symbol]                       0x000029a8   Section        0  usart.o(.text.uart0_send_char)
    __arm_cp.0_0                             0x000029d0   Number         4  usart.o(.text.uart0_send_char)
    [Anonymous Symbol]                       0x000029d4   Section        0  usart.o(.text.uart0_send_string)
    i.__0printf                              0x00002a10   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x00002a30   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_clz                              0x00002a58   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x00002a88   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00002a98   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00002aa0   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x00002ab1   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x00002ab0   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x00002c25   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x00002c24   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x00003311   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x00003310   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x00003331   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00003330   Section        0  printfa.o(i._printf_pre_padding)
    _sputc                                   0x0000335d   Thumb Code    10  printfa.o(i._sputc)
    i._sputc                                 0x0000335c   Section        0  printfa.o(i._sputc)
    gI2C_0ClockConfig                        0x00003366   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x00003366   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gPWM_0ClockConfig                        0x00003368   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x00003368   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x0000336c   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x0000336c   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gPWM_LEDClockConfig                      0x00003374   Data           3  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    [Anonymous Symbol]                       0x00003374   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    gPWM_LEDConfig                           0x00003378   Data           8  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    [Anonymous Symbol]                       0x00003378   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    gSPI_LCD_clockConfig                     0x00003380   Data           2  ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig)
    [Anonymous Symbol]                       0x00003380   Section        0  ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig)
    gSPI_LCD_config                          0x00003382   Data          10  ti_msp_dl_config.o(.rodata.gSPI_LCD_config)
    [Anonymous Symbol]                       0x00003382   Section        0  ti_msp_dl_config.o(.rodata.gSPI_LCD_config)
    gSYSPLLConfig                            0x0000338c   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x0000338c   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_0ClockConfig                      0x000033b4   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x000033b4   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x000033b8   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x000033b8   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gTIMER_TICKClockConfig                   0x000033cc   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    [Anonymous Symbol]                       0x000033cc   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    gTIMER_TICKTimerConfig                   0x000033d0   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    [Anonymous Symbol]                       0x000033d0   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    gUART_0ClockConfig                       0x000033e4   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x000033e4   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x000033e6   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000033e6   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    position_weights                         0x000033f0   Data          32  sensor.o(.rodata.position_weights)
    [Anonymous Symbol]                       0x000033f0   Section        0  sensor.o(.rodata.position_weights)
    [Anonymous Symbol]                       0x00003410   Section        0  empty.o(.rodata.str1.1)
    .data                                    0x20200000   Section        4  stdout.o(.data)
    TIMG0_IRQHandler.timer_count             0x20200004   Data           4  empty.o(.data.TIMG0_IRQHandler.timer_count)
    [Anonymous Symbol]                       0x20200004   Section        0  empty.o(.data.TIMG0_IRQHandler.timer_count)
    PID_operation.total_left_speed           0x20200014   Data           4  empty.o(.bss.PID_operation.total_left_speed)
    [Anonymous Symbol]                       0x20200014   Section        0  empty.o(.bss.PID_operation.total_left_speed)
    PID_operation.total_right_speed          0x20200018   Data           4  empty.o(.bss.PID_operation.total_right_speed)
    [Anonymous Symbol]                       0x20200018   Section        0  empty.o(.bss.PID_operation.total_right_speed)
    PID_operation.total_speed                0x2020001c   Data           4  empty.o(.bss.PID_operation.total_speed)
    [Anonymous Symbol]                       0x2020001c   Section        0  empty.o(.bss.PID_operation.total_speed)
    motor_encoder_L                          0x20200270   Data          16  bsp_motor_hallencoder.o(.bss.motor_encoder_L)
    [Anonymous Symbol]                       0x20200270   Section        0  bsp_motor_hallencoder.o(.bss.motor_encoder_L)
    motor_encoder_R                          0x20200280   Data          16  bsp_motor_hallencoder.o(.bss.motor_encoder_R)
    [Anonymous Symbol]                       0x20200280   Section        0  bsp_motor_hallencoder.o(.bss.motor_encoder_R)
    STACK                                    0x202003e0   Section     1024  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000000e1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memset                           0x000000e9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000000f7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000000f7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000000f7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000000fb   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x0000010d   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x000001af   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x000001b7   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x000001bf   Thumb Code   122  fmul.o(.text)
    __aeabi_fdiv                             0x00000239   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x000002b5   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x000003fd   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x00000409   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00000419   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x000004e9   Thumb Code   234  ddiv.o(.text)
    __aeabi_fcmple                           0x000005d9   Thumb Code    28  fcmple.o(.text)
    __aeabi_i2f                              0x000005f5   Thumb Code    22  fflti.o(.text)
    __aeabi_i2d                              0x0000060d   Thumb Code    34  dflti.o(.text)
    __aeabi_f2iz                             0x00000635   Thumb Code    50  ffixi.o(.text)
    __aeabi_d2iz                             0x00000669   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x000006b1   Thumb Code    40  f2d.o(.text)
    __aeabi_uidiv                            0x000006d9   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x000006d9   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x00000717   Thumb Code    96  uldiv.o(.text)
    __aeabi_llsl                             0x00000777   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x00000777   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x00000797   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x00000797   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x000007b9   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x000007b9   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x000007df   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x000007df   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x000007ef   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x00000861   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x0000087b   Thumb Code   164  depilogue.o(.text)
    __aeabi_d2ulz                            0x00000921   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x00000961   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00000989   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000989   Thumb Code     0  init.o(.text)
    AB_Control                               0x000009b9   Thumb Code   168  bsp_tb6612.o(.text.AB_Control)
    CalculatePositionFromDigital             0x00000a69   Thumb Code   124  sensor.o(.text.CalculatePositionFromDigital)
    DL_Common_delayCycles                    0x00000ae9   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_I2C_fillControllerTXFIFO              0x00000e15   Thumb Code    44  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    DL_I2C_flushControllerTXFIFO             0x00000e41   Thumb Code    36  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    DL_I2C_setClockConfig                    0x00000ed3   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SPI_init                              0x00000fdd   Thumb Code    68  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_setClockConfig                    0x00001071   Thumb Code    18  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SYSCTL_configSYSPLL                   0x000010cd   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x000011f1   Thumb Code    80  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00001275   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_initFourCCPWMMode               0x00001339   Thumb Code   264  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_initTimerMode                   0x00001441   Thumb Code   236  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00001551   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x0000156d   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00001585   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00001595   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x0000164d   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00001725   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    GROUP1_IRQHandler                        0x0000176d   Thumb Code   292  bsp_motor_hallencoder.o(.text.GROUP1_IRQHandler)
    IIC_Get_Digtal                           0x0000189d   Thumb Code    24  hardware_iic.o(.text.IIC_Get_Digtal)
    IIC_ReadByte                             0x000018b5   Thumb Code    28  hardware_iic.o(.text.IIC_ReadByte)
    Kalman_Init                              0x000018d1   Thumb Code    42  filter.o(.text.Kalman_Init)
    Kalman_Update                            0x000018fb   Thumb Code   108  filter.o(.text.Kalman_Update)
    LCD_WR_DATA8                             0x00001967   Thumb Code    20  hw_lcd.o(.text.LCD_WR_DATA8)
    LCD_WR_REG                               0x0000197d   Thumb Code    44  hw_lcd.o(.text.LCD_WR_REG)
    LCD_Writ_Bus                             0x000019a9   Thumb Code    44  hw_lcd.o(.text.LCD_Writ_Bus)
    PID_Calculate                            0x000019d9   Thumb Code   180  pid.o(.text.PID_Calculate)
    PID_Init                                 0x00001a91   Thumb Code    44  pid.o(.text.PID_Init)
    PID_operation                            0x00001abd   Thumb Code   284  empty.o(.text.PID_operation)
    Ping                                     0x00001c11   Thumb Code    50  hardware_iic.o(.text.Ping)
    SYSCFG_DL_GPIO_init                      0x00001c45   Thumb Code   456  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_0_init                     0x00001e29   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_PWM_0_init                     0x00001e7d   Thumb Code   132  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_PWM_LED_init                   0x00001f09   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    SYSCFG_DL_SPI_LCD_init                   0x00001f71   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    SYSCFG_DL_SYSCTL_init                    0x00001fad   Thumb Code    84  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00002005   Thumb Code    14  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00002015   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_TIMER_TICK_init                0x00002045   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    SYSCFG_DL_UART_0_init                    0x0000207d   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x000020c1   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00002119   Thumb Code   140  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    TIMA0_IRQHandler                         0x000021bd   Thumb Code    28  hw_timer.o(.text.TIMA0_IRQHandler)
    TIMG0_IRQHandler                         0x000021dd   Thumb Code   140  empty.o(.text.TIMG0_IRQHandler)
    UART0_IRQHandler                         0x00002291   Thumb Code    40  empty.o(.text.UART0_IRQHandler)
    UpdatePosition                           0x000022c1   Thumb Code    28  sensor.o(.text.UpdatePosition)
    delay_ms                                 0x00002459   Thumb Code    22  time.o(.text.delay_ms)
    delay_us                                 0x00002471   Thumb Code   108  time.o(.text.delay_us)
    encoder_init                             0x000024e5   Thumb Code    22  bsp_motor_hallencoder.o(.text.encoder_init)
    encoder_update_L                         0x000024fd   Thumb Code    24  bsp_motor_hallencoder.o(.text.encoder_update_L)
    encoder_update_R                         0x00002515   Thumb Code    24  bsp_motor_hallencoder.o(.text.encoder_update_R)
    fputc                                    0x0000252d   Thumb Code    40  usart.o(.text.fputc)
    get_encoder_count_L                      0x00002559   Thumb Code    24  bsp_motor_hallencoder.o(.text.get_encoder_count_L)
    get_encoder_count_R                      0x00002575   Thumb Code    24  bsp_motor_hallencoder.o(.text.get_encoder_count_R)
    hardware_IIC_ReadByte                    0x00002595   Thumb Code   172  hw_i2c.o(.text.hardware_IIC_ReadByte)
    lcd_init                                 0x00002645   Thumb Code   448  hw_lcd.o(.text.lcd_init)
    main                                     0x00002815   Thumb Code   276  empty.o(.text.main)
    spi_write_bus                            0x00002965   Thumb Code    40  hw_lcd.o(.text.spi_write_bus)
    timer_init                               0x00002991   Thumb Code    22  hw_timer.o(.text.timer_init)
    uart0_send_char                          0x000029a9   Thumb Code    40  usart.o(.text.uart0_send_char)
    uart0_send_string                        0x000029d5   Thumb Code    60  usart.o(.text.uart0_send_string)
    __0printf                                0x00002a11   Thumb Code    24  printfa.o(i.__0printf)
    __1printf                                0x00002a11   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x00002a11   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x00002a11   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x00002a11   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x00002a31   Thumb Code    36  printfa.o(i.__0sprintf)
    __1sprintf                               0x00002a31   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x00002a31   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x00002a31   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x00002a31   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_clz                                0x00002a59   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x00002a89   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00002a99   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00002aa1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x0000345c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0000347c   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20200000   Data           4  stdout.o(.data)
    Num_L                                    0x20200008   Data           4  hw_timer.o(.bss.Num_L)
    Num_R                                    0x2020000c   Data           4  hw_timer.o(.bss.Num_R)
    PID_flash_time                           0x20200010   Data           1  empty.o(.bss.PID_flash_time)
    bmq_flash_time                           0x20200020   Data           1  empty.o(.bss.bmq_flash_time)
    gPWM_0Backup                             0x20200024   Data         160  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    gPWM_LEDBackup                           0x202000c4   Data         160  ti_msp_dl_config.o(.bss.gPWM_LEDBackup)
    gSPI_LCDBackup                           0x20200164   Data          40  ti_msp_dl_config.o(.bss.gSPI_LCDBackup)
    gTIMER_TICKBackup                        0x2020018c   Data         188  ti_msp_dl_config.o(.bss.gTIMER_TICKBackup)
    led_flash_time                           0x20200248   Data           1  empty.o(.bss.led_flash_time)
    left_kalman                              0x2020024c   Data          16  empty.o(.bss.left_kalman)
    left_speed_pid                           0x2020025c   Data          20  empty.o(.bss.left_speed_pid)
    posion_kalman                            0x20200290   Data          16  empty.o(.bss.posion_kalman)
    posion_pid                               0x202002a0   Data          20  empty.o(.bss.posion_pid)
    right_kalman                             0x202002b4   Data          16  empty.o(.bss.right_kalman)
    right_speed_pid                          0x202002c4   Data          20  empty.o(.bss.right_speed_pid)
    rx_buff                                  0x202002d8   Data         256  empty.o(.bss.rx_buff)
    uart_data                                0x202003d8   Data           1  empty.o(.bss.uart_data)
    __initial_sp                             0x202007e0   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00003488, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x0000347c, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           52    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO          853  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO          926    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO          929    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          931    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          933    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO          934    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          936    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          938    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO          927    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO           53    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000024   Code   RO          864    .text               mc_p.l(memseta.o)
    0x0000010c   0x0000010c   0x000000b2   Code   RO          896    .text               mf_p.l(fadd.o)
    0x000001be   0x000001be   0x0000007a   Code   RO          898    .text               mf_p.l(fmul.o)
    0x00000238   0x00000238   0x0000007c   Code   RO          900    .text               mf_p.l(fdiv.o)
    0x000002b4   0x000002b4   0x00000164   Code   RO          902    .text               mf_p.l(dadd.o)
    0x00000418   0x00000418   0x000000d0   Code   RO          904    .text               mf_p.l(dmul.o)
    0x000004e8   0x000004e8   0x000000f0   Code   RO          906    .text               mf_p.l(ddiv.o)
    0x000005d8   0x000005d8   0x0000001c   Code   RO          908    .text               mf_p.l(fcmple.o)
    0x000005f4   0x000005f4   0x00000016   Code   RO          910    .text               mf_p.l(fflti.o)
    0x0000060a   0x0000060a   0x00000002   PAD
    0x0000060c   0x0000060c   0x00000028   Code   RO          912    .text               mf_p.l(dflti.o)
    0x00000634   0x00000634   0x00000032   Code   RO          916    .text               mf_p.l(ffixi.o)
    0x00000666   0x00000666   0x00000002   PAD
    0x00000668   0x00000668   0x00000048   Code   RO          920    .text               mf_p.l(dfixi.o)
    0x000006b0   0x000006b0   0x00000028   Code   RO          922    .text               mf_p.l(f2d.o)
    0x000006d8   0x000006d8   0x0000003e   Code   RO          945    .text               mc_p.l(uidiv_div0.o)
    0x00000716   0x00000716   0x00000060   Code   RO          951    .text               mc_p.l(uldiv.o)
    0x00000776   0x00000776   0x00000020   Code   RO          953    .text               mc_p.l(llshl.o)
    0x00000796   0x00000796   0x00000022   Code   RO          955    .text               mc_p.l(llushr.o)
    0x000007b8   0x000007b8   0x00000026   Code   RO          957    .text               mc_p.l(llsshr.o)
    0x000007de   0x000007de   0x00000000   Code   RO          966    .text               mc_p.l(iusefp.o)
    0x000007de   0x000007de   0x00000082   Code   RO          967    .text               mf_p.l(fepilogue.o)
    0x00000860   0x00000860   0x000000be   Code   RO          969    .text               mf_p.l(depilogue.o)
    0x0000091e   0x0000091e   0x00000002   PAD
    0x00000920   0x00000920   0x00000040   Code   RO          975    .text               mf_p.l(dfixul.o)
    0x00000960   0x00000960   0x00000028   Code   RO          977    .text               mf_p.l(cdrcmple.o)
    0x00000988   0x00000988   0x00000030   Code   RO          979    .text               mc_p.l(init.o)
    0x000009b8   0x000009b8   0x000000b0   Code   RO          468    .text.AB_Control    bsp_tb6612.o
    0x00000a68   0x00000a68   0x00000080   Code   RO          570    .text.CalculatePositionFromDigital  sensor.o
    0x00000ae8   0x00000ae8   0x0000000a   Code   RO          582    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00000af2   0x00000af2   0x00000028   Code   RO          196    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x00000b1a   0x00000b1a   0x00000028   Code   RO          549    .text.DL_Common_updateReg  hw_i2c.o
    0x00000b42   0x00000b42   0x00000002   PAD
    0x00000b44   0x00000b44   0x00000018   Code   RO          134    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x00000b5c   0x00000b5c   0x0000001c   Code   RO          453    .text.DL_GPIO_clearInterruptStatus  bsp_motor_hallencoder.o
    0x00000b78   0x00000b78   0x00000014   Code   RO          128    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x00000b8c   0x00000b8c   0x00000014   Code   RO          320    .text.DL_GPIO_clearPins  hw_lcd.o
    0x00000ba0   0x00000ba0   0x00000014   Code   RO          470    .text.DL_GPIO_clearPins  bsp_tb6612.o
    0x00000bb4   0x00000bb4   0x00000018   Code   RO          116    .text.DL_GPIO_enableHiZ  ti_msp_dl_config.o
    0x00000bcc   0x00000bcc   0x0000001c   Code   RO          136    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x00000be8   0x00000be8   0x00000018   Code   RO          112    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00000c00   0x00000c00   0x00000014   Code   RO           98    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00000c14   0x00000c14   0x00000018   Code   RO          449    .text.DL_GPIO_getEnabledInterruptStatus  bsp_motor_hallencoder.o
    0x00000c2c   0x00000c2c   0x00000030   Code   RO          122    .text.DL_GPIO_initDigitalInputFeatures  ti_msp_dl_config.o
    0x00000c5c   0x00000c5c   0x00000014   Code   RO          124    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x00000c70   0x00000c70   0x0000002c   Code   RO          120    .text.DL_GPIO_initDigitalOutputFeatures  ti_msp_dl_config.o
    0x00000c9c   0x00000c9c   0x00000014   Code   RO          108    .text.DL_GPIO_initPeripheralAnalogFunction  ti_msp_dl_config.o
    0x00000cb0   0x00000cb0   0x00000018   Code   RO          118    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x00000cc8   0x00000cc8   0x00000038   Code   RO          114    .text.DL_GPIO_initPeripheralInputFunctionFeatures  ti_msp_dl_config.o
    0x00000d00   0x00000d00   0x0000001c   Code   RO          110    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x00000d1c   0x00000d1c   0x00000016   Code   RO          451    .text.DL_GPIO_readPins  bsp_motor_hallencoder.o
    0x00000d32   0x00000d32   0x00000002   PAD
    0x00000d34   0x00000d34   0x00000010   Code   RO           88    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00000d44   0x00000d44   0x0000001c   Code   RO          130    .text.DL_GPIO_setLowerPinsPolarity  ti_msp_dl_config.o
    0x00000d60   0x00000d60   0x00000014   Code   RO          126    .text.DL_GPIO_setPins  ti_msp_dl_config.o
    0x00000d74   0x00000d74   0x00000014   Code   RO          322    .text.DL_GPIO_setPins  hw_lcd.o
    0x00000d88   0x00000d88   0x00000018   Code   RO          466    .text.DL_GPIO_setPins  bsp_tb6612.o
    0x00000da0   0x00000da0   0x00000018   Code   RO          132    .text.DL_GPIO_setUpperPinsPolarity  ti_msp_dl_config.o
    0x00000db8   0x00000db8   0x00000018   Code   RO          164    .text.DL_I2C_enableAnalogGlitchFilter  ti_msp_dl_config.o
    0x00000dd0   0x00000dd0   0x00000014   Code   RO          176    .text.DL_I2C_enableController  ti_msp_dl_config.o
    0x00000de4   0x00000de4   0x00000018   Code   RO          174    .text.DL_I2C_enableControllerClockStretching  ti_msp_dl_config.o
    0x00000dfc   0x00000dfc   0x00000018   Code   RO          102    .text.DL_I2C_enablePower  ti_msp_dl_config.o
    0x00000e14   0x00000e14   0x0000002c   Code   RO          595    .text.DL_I2C_fillControllerTXFIFO  driverlib.a(dl_i2c.o)
    0x00000e40   0x00000e40   0x00000024   Code   RO          597    .text.DL_I2C_flushControllerTXFIFO  driverlib.a(dl_i2c.o)
    0x00000e64   0x00000e64   0x00000014   Code   RO          537    .text.DL_I2C_getControllerStatus  hw_i2c.o
    0x00000e78   0x00000e78   0x00000014   Code   RO          545    .text.DL_I2C_receiveControllerData  hw_i2c.o
    0x00000e8c   0x00000e8c   0x00000010   Code   RO           92    .text.DL_I2C_reset  ti_msp_dl_config.o
    0x00000e9c   0x00000e9c   0x00000010   Code   RO          166    .text.DL_I2C_resetControllerTransfer  ti_msp_dl_config.o
    0x00000eac   0x00000eac   0x00000026   Code   RO          162    .text.DL_I2C_setAnalogGlitchFilterPulseWidth  ti_msp_dl_config.o
    0x00000ed2   0x00000ed2   0x00000026   Code   RO          591    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x00000ef8   0x00000ef8   0x00000024   Code   RO          172    .text.DL_I2C_setControllerRXFIFOThreshold  ti_msp_dl_config.o
    0x00000f1c   0x00000f1c   0x00000028   Code   RO          170    .text.DL_I2C_setControllerTXFIFOThreshold  ti_msp_dl_config.o
    0x00000f44   0x00000f44   0x00000018   Code   RO          168    .text.DL_I2C_setTimerPeriod  ti_msp_dl_config.o
    0x00000f5c   0x00000f5c   0x00000050   Code   RO          539    .text.DL_I2C_startControllerTransfer  hw_i2c.o
    0x00000fac   0x00000fac   0x00000018   Code   RO          190    .text.DL_SPI_enable  ti_msp_dl_config.o
    0x00000fc4   0x00000fc4   0x00000018   Code   RO          106    .text.DL_SPI_enablePower  ti_msp_dl_config.o
    0x00000fdc   0x00000fdc   0x00000044   Code   RO          623    .text.DL_SPI_init   driverlib.a(dl_spi.o)
    0x00001020   0x00001020   0x00000018   Code   RO          316    .text.DL_SPI_isBusy  hw_lcd.o
    0x00001038   0x00001038   0x00000018   Code   RO           96    .text.DL_SPI_reset  ti_msp_dl_config.o
    0x00001050   0x00001050   0x00000020   Code   RO          186    .text.DL_SPI_setBitRateSerialClockDivider  ti_msp_dl_config.o
    0x00001070   0x00001070   0x00000012   Code   RO          625    .text.DL_SPI_setClockConfig  driverlib.a(dl_spi.o)
    0x00001082   0x00001082   0x00000002   PAD
    0x00001084   0x00001084   0x00000030   Code   RO          188    .text.DL_SPI_setFIFOThreshold  ti_msp_dl_config.o
    0x000010b4   0x000010b4   0x00000016   Code   RO          314    .text.DL_SPI_transmitData8  hw_lcd.o
    0x000010ca   0x000010ca   0x00000002   PAD
    0x000010cc   0x000010cc   0x000000c0   Code   RO          818    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x0000118c   0x0000118c   0x0000000c   Code   RO          144    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00001198   0x00001198   0x00000014   Code   RO          146    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x000011ac   0x000011ac   0x00000010   Code   RO          150    .text.DL_SYSCTL_enableMFCLK  ti_msp_dl_config.o
    0x000011bc   0x000011bc   0x00000018   Code   RO          138    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x000011d4   0x000011d4   0x0000001c   Code   RO          140    .text.DL_SYSCTL_setFlashWaitState  ti_msp_dl_config.o
    0x000011f0   0x000011f0   0x00000050   Code   RO          832    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00001240   0x00001240   0x0000001c   Code   RO          142    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x0000125c   0x0000125c   0x00000018   Code   RO          148    .text.DL_SYSCTL_setULPCLKDivider  ti_msp_dl_config.o
    0x00001274   0x00001274   0x00000028   Code   RO          826    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x0000129c   0x0000129c   0x0000000c   Code   RO          194    .text.DL_SYSTICK_enable  ti_msp_dl_config.o
    0x000012a8   0x000012a8   0x00000028   Code   RO          192    .text.DL_SYSTICK_init  ti_msp_dl_config.o
    0x000012d0   0x000012d0   0x00000014   Code   RO          156    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x000012e4   0x000012e4   0x00000018   Code   RO          160    .text.DL_Timer_enableInterrupt  ti_msp_dl_config.o
    0x000012fc   0x000012fc   0x00000018   Code   RO          100    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x00001314   0x00001314   0x00000012   Code   RO           18    .text.DL_Timer_getPendingInterrupt  empty.o
    0x00001326   0x00001326   0x00000012   Code   RO          487    .text.DL_Timer_getPendingInterrupt  hw_timer.o
    0x00001338   0x00001338   0x00000108   Code   RO          757    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x00001440   0x00001440   0x000000ec   Code   RO          681    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x0000152c   0x0000152c   0x00000010   Code   RO           90    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x0000153c   0x0000153c   0x00000014   Code   RO          158    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x00001550   0x00001550   0x0000001c   Code   RO          723    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x0000156c   0x0000156c   0x00000018   Code   RO          731    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00001584   0x00001584   0x00000010   Code   RO          683    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00001594   0x00001594   0x0000001c   Code   RO          677    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x000015b0   0x000015b0   0x0000003c   Code   RO          154    .text.DL_Timer_setCounterControl  ti_msp_dl_config.o
    0x000015ec   0x000015ec   0x00000016   Code   RO          184    .text.DL_UART_enable  ti_msp_dl_config.o
    0x00001602   0x00001602   0x00000002   PAD
    0x00001604   0x00001604   0x0000001c   Code   RO          182    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x00001620   0x00001620   0x00000018   Code   RO          104    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x00001638   0x00001638   0x00000012   Code   RO           12    .text.DL_UART_getPendingInterrupt  empty.o
    0x0000164a   0x0000164a   0x00000002   PAD
    0x0000164c   0x0000164c   0x00000048   Code   RO          778    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00001694   0x00001694   0x00000018   Code   RO          255    .text.DL_UART_isBusy  usart.o
    0x000016ac   0x000016ac   0x00000014   Code   RO           14    .text.DL_UART_receiveData  empty.o
    0x000016c0   0x000016c0   0x00000018   Code   RO           94    .text.DL_UART_reset  ti_msp_dl_config.o
    0x000016d8   0x000016d8   0x0000004c   Code   RO          180    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x00001724   0x00001724   0x00000012   Code   RO          780    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x00001736   0x00001736   0x0000001e   Code   RO          178    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x00001754   0x00001754   0x00000016   Code   RO          257    .text.DL_UART_transmitData  usart.o
    0x0000176a   0x0000176a   0x00000002   PAD
    0x0000176c   0x0000176c   0x00000130   Code   RO          447    .text.GROUP1_IRQHandler  bsp_motor_hallencoder.o
    0x0000189c   0x0000189c   0x00000018   Code   RO          519    .text.IIC_Get_Digtal  hardware_iic.o
    0x000018b4   0x000018b4   0x0000001c   Code   RO          509    .text.IIC_ReadByte  hardware_iic.o
    0x000018d0   0x000018d0   0x0000002a   Code   RO          498    .text.Kalman_Init   filter.o
    0x000018fa   0x000018fa   0x0000006c   Code   RO          500    .text.Kalman_Update  filter.o
    0x00001966   0x00001966   0x00000014   Code   RO          324    .text.LCD_WR_DATA8  hw_lcd.o
    0x0000197a   0x0000197a   0x00000002   PAD
    0x0000197c   0x0000197c   0x0000002c   Code   RO          328    .text.LCD_WR_REG    hw_lcd.o
    0x000019a8   0x000019a8   0x00000030   Code   RO          318    .text.LCD_Writ_Bus  hw_lcd.o
    0x000019d8   0x000019d8   0x000000b8   Code   RO          420    .text.PID_Calculate  pid.o
    0x00001a90   0x00001a90   0x0000002c   Code   RO          418    .text.PID_Init      pid.o
    0x00001abc   0x00001abc   0x00000154   Code   RO            8    .text.PID_operation  empty.o
    0x00001c10   0x00001c10   0x00000032   Code   RO          517    .text.Ping          hardware_iic.o
    0x00001c42   0x00001c42   0x00000002   PAD
    0x00001c44   0x00001c44   0x000001e4   Code   RO           64    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00001e28   0x00001e28   0x00000054   Code   RO           76    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x00001e7c   0x00001e7c   0x0000008c   Code   RO           70    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00001f08   0x00001f08   0x00000068   Code   RO           68    .text.SYSCFG_DL_PWM_LED_init  ti_msp_dl_config.o
    0x00001f70   0x00001f70   0x0000003c   Code   RO           80    .text.SYSCFG_DL_SPI_LCD_init  ti_msp_dl_config.o
    0x00001fac   0x00001fac   0x00000058   Code   RO           66    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00002004   0x00002004   0x0000000e   Code   RO           82    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00002012   0x00002012   0x00000002   PAD
    0x00002014   0x00002014   0x00000030   Code   RO           72    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00002044   0x00002044   0x00000038   Code   RO           74    .text.SYSCFG_DL_TIMER_TICK_init  ti_msp_dl_config.o
    0x0000207c   0x0000207c   0x00000044   Code   RO           78    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x000020c0   0x000020c0   0x00000058   Code   RO           60    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00002118   0x00002118   0x000000a4   Code   RO           62    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x000021bc   0x000021bc   0x00000020   Code   RO          485    .text.TIMA0_IRQHandler  hw_timer.o
    0x000021dc   0x000021dc   0x000000b4   Code   RO           16    .text.TIMG0_IRQHandler  empty.o
    0x00002290   0x00002290   0x00000030   Code   RO           10    .text.UART0_IRQHandler  empty.o
    0x000022c0   0x000022c0   0x0000001c   Code   RO          572    .text.UpdatePosition  sensor.o
    0x000022dc   0x000022dc   0x00000028   Code   RO            4    .text.__NVIC_ClearPendingIRQ  empty.o
    0x00002304   0x00002304   0x00000028   Code   RO          431    .text.__NVIC_ClearPendingIRQ  bsp_motor_hallencoder.o
    0x0000232c   0x0000232c   0x0000002c   Code   RO          481    .text.__NVIC_ClearPendingIRQ  hw_timer.o
    0x00002358   0x00002358   0x00000028   Code   RO            6    .text.__NVIC_EnableIRQ  empty.o
    0x00002380   0x00002380   0x00000028   Code   RO          433    .text.__NVIC_EnableIRQ  bsp_motor_hallencoder.o
    0x000023a8   0x000023a8   0x0000002c   Code   RO          483    .text.__NVIC_EnableIRQ  hw_timer.o
    0x000023d4   0x000023d4   0x00000084   Code   RO          152    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x00002458   0x00002458   0x00000016   Code   RO          561    .text.delay_ms      time.o
    0x0000246e   0x0000246e   0x00000002   PAD
    0x00002470   0x00002470   0x00000074   Code   RO          559    .text.delay_us      time.o
    0x000024e4   0x000024e4   0x00000016   Code   RO          429    .text.encoder_init  bsp_motor_hallencoder.o
    0x000024fa   0x000024fa   0x00000002   PAD
    0x000024fc   0x000024fc   0x00000018   Code   RO          443    .text.encoder_update_L  bsp_motor_hallencoder.o
    0x00002514   0x00002514   0x00000018   Code   RO          445    .text.encoder_update_R  bsp_motor_hallencoder.o
    0x0000252c   0x0000252c   0x0000002c   Code   RO          261    .text.fputc         usart.o
    0x00002558   0x00002558   0x0000001c   Code   RO          435    .text.get_encoder_count_L  bsp_motor_hallencoder.o
    0x00002574   0x00002574   0x00000020   Code   RO          437    .text.get_encoder_count_R  bsp_motor_hallencoder.o
    0x00002594   0x00002594   0x000000b0   Code   RO          543    .text.hardware_IIC_ReadByte  hw_i2c.o
    0x00002644   0x00002644   0x000001d0   Code   RO          332    .text.lcd_init      hw_lcd.o
    0x00002814   0x00002814   0x00000150   Code   RO            2    .text.main          empty.o
    0x00002964   0x00002964   0x0000002c   Code   RO          312    .text.spi_write_bus  hw_lcd.o
    0x00002990   0x00002990   0x00000016   Code   RO          479    .text.timer_init    hw_timer.o
    0x000029a6   0x000029a6   0x00000002   PAD
    0x000029a8   0x000029a8   0x0000002c   Code   RO          253    .text.uart0_send_char  usart.o
    0x000029d4   0x000029d4   0x0000003c   Code   RO          259    .text.uart0_send_string  usart.o
    0x00002a10   0x00002a10   0x00000020   Code   RO          869    i.__0printf         mc_p.l(printfa.o)
    0x00002a30   0x00002a30   0x00000028   Code   RO          871    i.__0sprintf        mc_p.l(printfa.o)
    0x00002a58   0x00002a58   0x0000002e   Code   RO          971    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00002a86   0x00002a86   0x00000002   PAD
    0x00002a88   0x00002a88   0x0000000e   Code   RO          983    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00002a96   0x00002a96   0x00000002   PAD
    0x00002a98   0x00002a98   0x00000002   Code   RO          984    i.__scatterload_null  mc_p.l(handlers.o)
    0x00002a9a   0x00002a9a   0x00000006   PAD
    0x00002aa0   0x00002aa0   0x0000000e   Code   RO          985    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x00002aae   0x00002aae   0x00000002   PAD
    0x00002ab0   0x00002ab0   0x00000174   Code   RO          876    i._fp_digits        mc_p.l(printfa.o)
    0x00002c24   0x00002c24   0x000006ec   Code   RO          877    i._printf_core      mc_p.l(printfa.o)
    0x00003310   0x00003310   0x00000020   Code   RO          878    i._printf_post_padding  mc_p.l(printfa.o)
    0x00003330   0x00003330   0x0000002c   Code   RO          879    i._printf_pre_padding  mc_p.l(printfa.o)
    0x0000335c   0x0000335c   0x0000000a   Code   RO          881    i._sputc            mc_p.l(printfa.o)
    0x00003366   0x00003366   0x00000002   Data   RO          211    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x00003368   0x00003368   0x00000003   Data   RO          205    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x0000336b   0x0000336b   0x00000001   PAD
    0x0000336c   0x0000336c   0x00000008   Data   RO          206    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x00003374   0x00003374   0x00000003   Data   RO          203    .rodata.gPWM_LEDClockConfig  ti_msp_dl_config.o
    0x00003377   0x00003377   0x00000001   PAD
    0x00003378   0x00003378   0x00000008   Data   RO          204    .rodata.gPWM_LEDConfig  ti_msp_dl_config.o
    0x00003380   0x00003380   0x00000002   Data   RO          214    .rodata.gSPI_LCD_clockConfig  ti_msp_dl_config.o
    0x00003382   0x00003382   0x0000000a   Data   RO          215    .rodata.gSPI_LCD_config  ti_msp_dl_config.o
    0x0000338c   0x0000338c   0x00000028   Data   RO          202    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x000033b4   0x000033b4   0x00000003   Data   RO          207    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x000033b7   0x000033b7   0x00000001   PAD
    0x000033b8   0x000033b8   0x00000014   Data   RO          208    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x000033cc   0x000033cc   0x00000003   Data   RO          209    .rodata.gTIMER_TICKClockConfig  ti_msp_dl_config.o
    0x000033cf   0x000033cf   0x00000001   PAD
    0x000033d0   0x000033d0   0x00000014   Data   RO          210    .rodata.gTIMER_TICKTimerConfig  ti_msp_dl_config.o
    0x000033e4   0x000033e4   0x00000002   Data   RO          212    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x000033e6   0x000033e6   0x0000000a   Data   RO          213    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000033f0   0x000033f0   0x00000020   Data   RO          574    .rodata.position_weights  sensor.o
    0x00003410   0x00003410   0x00000049   Data   RO           33    .rodata.str1.1      empty.o
    0x00003459   0x00003459   0x00000003   PAD
    0x0000345c   0x0000345c   0x00000020   Data   RO          982    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00003480, Size: 0x000007e0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00003480   0x00000004   Data   RW          940    .data               mc_p.l(stdout.o)
    0x20200004   0x00003484   0x00000004   Data   RW           38    .data.TIMG0_IRQHandler.timer_count  empty.o
    0x20200008        -       0x00000004   Zero   RW          489    .bss.Num_L          hw_timer.o
    0x2020000c        -       0x00000004   Zero   RW          490    .bss.Num_R          hw_timer.o
    0x20200010        -       0x00000001   Zero   RW           22    .bss.PID_flash_time  empty.o
    0x20200011   0x00003488   0x00000003   PAD
    0x20200014        -       0x00000004   Zero   RW           34    .bss.PID_operation.total_left_speed  empty.o
    0x20200018        -       0x00000004   Zero   RW           35    .bss.PID_operation.total_right_speed  empty.o
    0x2020001c        -       0x00000004   Zero   RW           36    .bss.PID_operation.total_speed  empty.o
    0x20200020        -       0x00000001   Zero   RW           21    .bss.bmq_flash_time  empty.o
    0x20200021   0x00003488   0x00000003   PAD
    0x20200024        -       0x000000a0   Zero   RW          199    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x202000c4        -       0x000000a0   Zero   RW          198    .bss.gPWM_LEDBackup  ti_msp_dl_config.o
    0x20200164        -       0x00000028   Zero   RW          201    .bss.gSPI_LCDBackup  ti_msp_dl_config.o
    0x2020018c        -       0x000000bc   Zero   RW          200    .bss.gTIMER_TICKBackup  ti_msp_dl_config.o
    0x20200248        -       0x00000001   Zero   RW           20    .bss.led_flash_time  empty.o
    0x20200249   0x00003488   0x00000003   PAD
    0x2020024c        -       0x00000010   Zero   RW           31    .bss.left_kalman    empty.o
    0x2020025c        -       0x00000014   Zero   RW           28    .bss.left_speed_pid  empty.o
    0x20200270        -       0x00000010   Zero   RW          455    .bss.motor_encoder_L  bsp_motor_hallencoder.o
    0x20200280        -       0x00000010   Zero   RW          456    .bss.motor_encoder_R  bsp_motor_hallencoder.o
    0x20200290        -       0x00000010   Zero   RW           30    .bss.posion_kalman  empty.o
    0x202002a0        -       0x00000014   Zero   RW           37    .bss.posion_pid     empty.o
    0x202002b4        -       0x00000010   Zero   RW           32    .bss.right_kalman   empty.o
    0x202002c4        -       0x00000014   Zero   RW           29    .bss.right_speed_pid  empty.o
    0x202002d8        -       0x00000100   Zero   RW           26    .bss.rx_buff        empty.o
    0x202003d8        -       0x00000001   Zero   RW           24    .bss.uart_data      empty.o
    0x202003d9   0x00003488   0x00000007   PAD
    0x202003e0        -       0x00000400   Zero   RW           50    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       588         32          0          0         32       6700   bsp_motor_hallencoder.o
       220         12          0          0          0       5954   bsp_tb6612.o
      1040        168         73          4        380       9815   empty.o
       150          0          0          0          0       1009   filter.o
       102          0          0          0          0       1957   hardware_iic.o
       336         28          0          0          0       5014   hw_i2c.o
       706         28          0          0          0      15921   hw_lcd.o
       160         12          0          0          8       5500   hw_timer.o
       228          4          0          0          0       1078   pid.o
       156          4         32          0          0       1057   sensor.o
        20          4        192          0       1024        616   startup_mspm0g350x_uvision.o
      3024        268        134          0        548      37381   ti_msp_dl_config.o
       138          8          0          0          0       1114   time.o
       194         12          0          0          0       3373   usart.o

    ----------------------------------------------------------------------
      7086        <USER>        <GROUP>          4       2008      96489   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          7          0         16          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        10          0          0          0          0        803   dl_common.o
       118          0          0          0          0       8620   dl_i2c.o
        86          8          0          0          0      13518   dl_spi.o
       312         24          0          0          0      12877   dl_sysctl_mspm0g1x0x_g3x0x.o
       596        188          0          0          0      41557   dl_timer.o
        90          8          0          0          0      14163   dl_uart.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0        100   memseta.o
      2302        102          0          0          0        556   printfa.o
         0          0          0          4          0          0   stdout.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        72         10          0          0          0         72   dfixi.o
        64         10          0          0          0         68   dfixul.o
        40          6          0          0          0         68   dflti.o
       208          6          0          0          0         88   dmul.o
        40          0          0          0          0         60   f2d.o
       178          0          0          0          0        108   fadd.o
        28          0          0          0          0         60   fcmple.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        50          0          0          0          0         60   ffixi.o
        22          0          0          0          0         68   fflti.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      5880        <USER>          <GROUP>          4          0      94070   Library Totals
        20          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1212        228          0          0          0      91538   driverlib.a
      2698        120          0          4          0       1084   mc_p.l
      1950         44          0          0          0       1448   mf_p.l

    ----------------------------------------------------------------------
      5880        <USER>          <GROUP>          4          0      94070   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12966        972        470          8       2008     189111   Grand Totals
     12966        972        470          8       2008     189111   ELF Image Totals
     12966        972        470          8          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13436 (  13.12kB)
    Total RW  Size (RW Data + ZI Data)              2016 (   1.97kB)
    Total ROM Size (Code + RO Data + RW Data)      13444 (  13.13kB)

==============================================================================

